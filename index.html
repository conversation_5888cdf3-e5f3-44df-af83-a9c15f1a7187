<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>荣联科技新闻中心</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  </head>
  <body>
    <div style="padding: 20px; background: yellow; margin: 10px;">
      <h1>🔥 HTML 静态内容测试</h1>
      <p>如果您能看到这段黄色背景的文字，说明 HTML 页面可以正常加载。</p>
    </div>
    <div id="app">
      <div style="padding: 20px; background: lightblue; margin: 10px;">
        <h2>⏳ Vue 3 应用加载中...</h2>
        <p>如果 Vue 应用正常工作，这段文字应该会被替换。</p>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
    <script>
      console.log('🌐 HTML 页面已加载');
      console.log('📍 当前URL:', window.location.href);

      // 检查 Vue 应用是否挂载
      setTimeout(() => {
        const app = document.getElementById('app');
        if (app && app.innerHTML.includes('Vue 3 应用加载中')) {
          console.error('❌ Vue 3 应用可能没有正确挂载');
        } else {
          console.log('✅ Vue 3 应用已挂载');
        }
      }, 2000);
    </script>
  </body>
</html>
