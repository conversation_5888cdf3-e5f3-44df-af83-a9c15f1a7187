<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue 3 独立测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: 2px solid #4CAF50;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🧪 Vue 3 独立测试页面</h1>
    
    <div class="test-section">
        <h2>📊 CDN版本测试</h2>
        <div id="vue-app">
            <h3>✅ Vue 3 CDN版本正常工作！</h3>
            <p>当前时间: {{ currentTime }}</p>
            <p>计数器: {{ count }}</p>
            <button @click="updateTime">更新时间</button>
            <button @click="increment">计数 +1</button>
            <div v-if="count > 5" style="background: yellow; padding: 10px; margin: 10px 0;">
                🎉 计数器超过5了！Vue响应式正常工作！
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🔗 Vite开发服务器测试</h2>
        <button onclick="testViteServer()">测试Vite服务器</button>
        <div id="vite-test-result"></div>
    </div>

    <div class="test-section">
        <h2>📝 控制台日志</h2>
        <div id="console-output"></div>
        <button onclick="clearConsole()">清空日志</button>
    </div>

    <script>
        // Vue 3 CDN版本测试
        const { createApp } = Vue;

        createApp({
            data() {
                return {
                    currentTime: new Date().toLocaleString(),
                    count: 0
                }
            },
            methods: {
                updateTime() {
                    this.currentTime = new Date().toLocaleString();
                    console.log('✅ Vue CDN版本 - 时间已更新:', this.currentTime);
                },
                increment() {
                    this.count++;
                    console.log('✅ Vue CDN版本 - 计数器:', this.count);
                }
            },
            mounted() {
                console.log('✅ Vue 3 CDN版本应用已挂载');
            }
        }).mount('#vue-app');

        // 测试Vite开发服务器
        function testViteServer() {
            const resultDiv = document.getElementById('vite-test-result');
            resultDiv.innerHTML = '<p>测试中...</p>';

            fetch('http://localhost:3000/')
                .then(response => response.text())
                .then(html => {
                    if (html.includes('Vue')) {
                        resultDiv.innerHTML = '<div class="success">✅ Vite服务器正常，页面包含Vue相关内容</div>';
                    } else {
                        resultDiv.innerHTML = '<div class="error">⚠️ Vite服务器正常，但页面可能没有Vue内容</div>';
                    }
                    console.log('Vite服务器响应长度:', html.length);
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="error">❌ 无法连接到Vite服务器: ${error.message}</div>`;
                    console.error('Vite服务器测试失败:', error);
                });
        }

        // 控制台日志捕获
        const logs = [];
        const originalLog = console.log;
        const originalError = console.error;

        console.log = function(...args) {
            originalLog.apply(console, args);
            logs.push({ type: 'log', message: args.join(' '), time: new Date().toLocaleTimeString() });
            updateConsoleOutput();
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            logs.push({ type: 'error', message: args.join(' '), time: new Date().toLocaleTimeString() });
            updateConsoleOutput();
        };

        function updateConsoleOutput() {
            const output = document.getElementById('console-output');
            output.innerHTML = logs.slice(-10).map(log => 
                `<div class="${log.type === 'error' ? 'error' : 'success'}" style="padding: 5px; margin: 2px 0;">
                    [${log.time}] ${log.type.toUpperCase()}: ${log.message}
                </div>`
            ).join('');
        }

        function clearConsole() {
            logs.length = 0;
            updateConsoleOutput();
        }

        // 页面加载完成
        console.log('🌐 Vue 3 独立测试页面已加载');
    </script>
</body>
</html>
