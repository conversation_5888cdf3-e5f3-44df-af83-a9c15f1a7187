# 域名更新说明

## 更新内容

用户邮箱域名已从 `company.com` 更新为 `ronglian.com`。

## 更新的文件

### 1. 模拟数据 (`src/lib/mockData.ts`)
- 管理员邮箱: `<EMAIL>` → `<EMAIL>`
- 编辑邮箱: `<EMAIL>` → `<EMAIL>`
- 编辑2邮箱: `<EMAIL>` → `<EMAIL>`

### 2. 数据库初始化脚本 (`database/schema.sql`)
- 初始用户数据中的邮箱域名已更新

### 3. 用户指南 (`USER_GUIDE.md`)
- 演示账户信息已更新

### 4. 登录组件 (`src/components/LoginForm.tsx`)
- 登录页面显示的演示账户信息已更新

## 新的演示账户

| 角色 | 邮箱 | 密码 | 权限 |
|------|------|------|------|
| 管理员 | <EMAIL> | admin123 | 全部功能 |
| 编辑 | <EMAIL> | editor123 | 新闻创建，需审批 |
| 编辑2 | <EMAIL> | editor123 | 新闻创建，需审批 |

## 注意事项

1. **现有数据**: 如果您已经在使用系统并创建了数据，请注意这个更改只影响演示账户
2. **生产环境**: 在生产环境中部署时，请确保更新 Supabase 数据库中的相应用户数据
3. **登录**: 请使用新的邮箱地址登录系统

## 验证更新

1. 重启开发服务器
2. 访问 http://localhost:3000
3. 使用新的邮箱地址登录：
   - 管理员: `<EMAIL>` / `admin123`
   - 编辑: `<EMAIL>` / `editor123`

## 技术细节

更新涉及以下技术组件：
- React 组件中的硬编码邮箱地址
- 模拟数据中的用户信息
- 数据库初始化脚本
- 文档和用户指南

所有更改都已完成，系统现在使用 `ronglian.com` 域名作为用户邮箱后缀。
