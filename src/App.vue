<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

onMounted(async () => {
  try {
    console.log('🎉 Vue 3 应用已挂载')
    // 初始化认证状态
    await authStore.initializeAuth()
    console.log('✅ 认证状态初始化完成')
  } catch (error) {
    console.error('❌ 应用初始化失败:', error)
  }
})
</script>
