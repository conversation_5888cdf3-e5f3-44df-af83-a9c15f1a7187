<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <div class="p-8">
      <h1 class="text-3xl font-bold text-blue-600 mb-4">荣联科技新闻中心</h1>
      <p class="text-gray-600 mb-4">Vue 3 版本正在加载...</p>
      <div class="bg-white p-4 rounded-lg shadow">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

onMounted(async () => {
  try {
    console.log('Vue 3 应用已挂载')
    // 初始化认证状态
    await authStore.initializeAuth()
    console.log('认证状态初始化完成')
  } catch (error) {
    console.error('应用初始化失败:', error)
  }
})
</script>
