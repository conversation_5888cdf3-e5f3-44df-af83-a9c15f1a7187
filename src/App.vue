<template>
  <div id="app" class="min-h-screen bg-gray-50">
    <div style="padding: 20px; background: lightblue; margin: 10px; border: 2px solid blue;">
      <h2 style="color: blue;">🎯 Vue 3 App.vue 组件已加载</h2>
      <p>当前路由: {{ $route.path }}</p>
    </div>
    <router-view />
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'

onMounted(() => {
  console.log('✅ App.vue 组件已挂载')
  console.log('当前路由:', window.location.pathname)
})
</script>
