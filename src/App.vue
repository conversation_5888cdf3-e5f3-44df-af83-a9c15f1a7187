<template>
  <div id="app">
    <h1 style="color: red; font-size: 24px; padding: 20px;">🔥 Vue 3 测试页面</h1>
    <div style="padding: 20px; background: #f0f0f0; margin: 20px;">
      <p>如果您能看到这段文字，说明 Vue 3 应用正在运行！</p>
      <p>当前时间: {{ currentTime }}</p>
      <button @click="updateTime" style="background: blue; color: white; padding: 10px; border: none; cursor: pointer;">
        更新时间
      </button>
    </div>
    <div style="padding: 20px;">
      <router-view />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const currentTime = ref(new Date().toLocaleString())

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
  console.log('时间已更新:', currentTime.value)
}

onMounted(() => {
  console.log('🎉 Vue 3 应用已成功挂载！')
  console.log('当前路由:', window.location.pathname)
})
</script>
