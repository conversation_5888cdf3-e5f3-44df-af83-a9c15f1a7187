import { supabase, isSupabaseConfigured, TABLES } from './vue-supabase'
import { mockNewsApi, mockCategoryApi, mockUserApi } from './vue-mockData'
import type { 
  NewsWithDetails, 
  NewsFilters, 
  CreateNewsData, 
  UpdateNewsData,
  News,
  Category,
  User,
  LoginData
} from '@/types/vue-types'

// 新闻相关 API
export const newsApi = {
  // 获取新闻列表
  async getNews(filters: NewsFilters = {}) {
    if (!isSupabaseConfigured()) {
      return mockNewsApi.getNews(filters)
    }

    let query = supabase
      .from(TABLES.NEWS)
      .select(`
        *,
        category:categories(*),
        author:users(*)
      `)
      .order('created_at', { ascending: false })

    if (filters.category_id) {
      query = query.eq('category_id', filters.category_id)
    }

    if (filters.status) {
      query = query.eq('status', filters.status)
    }

    if (filters.search) {
      query = query.or(`title.ilike.%${filters.search}%,content.ilike.%${filters.search}%`)
    }

    const limit = filters.limit || 10
    const offset = ((filters.page || 1) - 1) * limit

    query = query.range(offset, offset + limit - 1)

    const { data, error } = await query

    if (error) throw error
    return data as NewsWithDetails[]
  },

  // 获取单个新闻
  async getNewsById(id: string) {
    if (!isSupabaseConfigured()) {
      return mockNewsApi.getNewsById(id)
    }

    const { data, error } = await supabase
      .from(TABLES.NEWS)
      .select(`
        *,
        category:categories(*),
        author:users(*)
      `)
      .eq('id', id)
      .single()

    if (error) throw error
    return data as NewsWithDetails
  },

  // 创建新闻
  async createNews(newsData: CreateNewsData) {
    if (!isSupabaseConfigured()) {
      return mockNewsApi.createNews(newsData)
    }

    const { data, error } = await supabase
      .from(TABLES.NEWS)
      .insert([{
        ...newsData,
        author_id: 'current_user_id',
        view_count: 0,
        published_at: newsData.status === 'published' ? new Date().toISOString() : null,
      }])
      .select()
      .single()

    if (error) throw error
    return data as News
  },

  // 更新新闻
  async updateNews(newsData: UpdateNewsData) {
    if (!isSupabaseConfigured()) {
      return mockNewsApi.updateNews(newsData)
    }

    const { id, ...updateData } = newsData

    const { data, error } = await supabase
      .from(TABLES.NEWS)
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
        published_at: updateData.status === 'published' ? new Date().toISOString() : undefined,
      })
      .eq('id', id)
      .select()
      .single()

    if (error) throw error
    return data as News
  },

  // 删除新闻
  async deleteNews(id: string) {
    if (!isSupabaseConfigured()) {
      return mockNewsApi.deleteNews(id)
    }

    const { error } = await supabase
      .from(TABLES.NEWS)
      .delete()
      .eq('id', id)

    if (error) throw error
  },

  // 增加浏览量
  async incrementViewCount(id: string) {
    if (!isSupabaseConfigured()) {
      return mockNewsApi.incrementViewCount(id)
    }

    const { error } = await supabase.rpc('increment_view_count', { news_id: id })
    if (error) throw error
  },

  // 批准新闻
  async approveNews(newsId: string) {
    if (!isSupabaseConfigured()) {
      return mockNewsApi.approveNews(newsId)
    }

    const { data, error } = await supabase
      .from(TABLES.NEWS)
      .update({
        status: 'published',
        published_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', newsId)
      .select()
      .single()

    if (error) throw error
    return data as News
  },

  // 拒绝新闻
  async rejectNews(newsId: string, reason: string) {
    if (!isSupabaseConfigured()) {
      return mockNewsApi.rejectNews(newsId, reason)
    }

    const { data, error } = await supabase
      .from(TABLES.NEWS)
      .update({
        status: 'rejected',
        rejection_reason: reason,
        updated_at: new Date().toISOString(),
      })
      .eq('id', newsId)
      .select()
      .single()

    if (error) throw error
    return data as News
  },
}

// 分类相关 API
export const categoryApi = {
  async getCategories() {
    if (!isSupabaseConfigured()) {
      return mockCategoryApi.getCategories()
    }

    const { data, error } = await supabase
      .from(TABLES.CATEGORIES)
      .select('*')
      .order('created_at', { ascending: true })

    if (error) throw error
    return data as Category[]
  },
}

// 用户相关 API
export const userApi = {
  async getCurrentUser() {
    if (!isSupabaseConfigured()) {
      return mockUserApi.getCurrentUser()
    }

    const { data: { user } } = await supabase.auth.getUser()
    if (!user) return null

    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .eq('id', user.id)
      .single()

    if (error) throw error
    return data as User
  },

  async signIn(email: string, password: string) {
    if (!isSupabaseConfigured()) {
      return mockUserApi.signIn(email, password)
    }

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    })

    if (error) throw error

    // 获取用户详细信息
    const { data: userData, error: userError } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .eq('id', data.user.id)
      .single()

    if (userError) throw userError

    return { user: userData as User }
  },

  async signOut() {
    if (!isSupabaseConfigured()) {
      return mockUserApi.signOut()
    }

    const { error } = await supabase.auth.signOut()
    if (error) throw error
  },

  async getAllUsers() {
    if (!isSupabaseConfigured()) {
      return mockUserApi.getAllUsers()
    }

    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .order('created_at', { ascending: false })

    if (error) throw error
    return data as User[]
  },
}
