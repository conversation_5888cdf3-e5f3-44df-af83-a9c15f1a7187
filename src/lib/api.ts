import { supabase, TABLES } from './supabase';
import { mockNews<PERSON>pi, mockCategory<PERSON>pi, mockUser<PERSON>pi } from './mockData';
import type { News, Category, User, CreateNewsData, UpdateNewsData, NewsFilters, NewsWithDetails, CreateUserData, UpdateUserData } from '@/types';

// 检查是否有有效的 Supabase 配置
const isSupabaseConfigured = () => {
  // 如果是演示模式，直接使用模拟数据
  if (process.env.NEXT_PUBLIC_DEMO_MODE === 'true') {
    return false;
  }

  const url = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const key = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  return url && key && url !== 'your_supabase_url_here' && key !== 'your_supabase_anon_key_here';
};

// 新闻相关 API
export const newsApi = {
  // 获取新闻列表
  async getNews(filters: NewsFilters = {}) {
    if (!isSupabaseConfigured()) {
      return mockNewsApi.getNews(filters);
    }

    let query = supabase
      .from(TABLES.NEWS)
      .select(`
        *,
        category:categories(*),
        author:users(*)
      `)
      .order('created_at', { ascending: false });

    if (filters.category_id) {
      query = query.eq('category_id', filters.category_id);
    }

    if (filters.status) {
      query = query.eq('status', filters.status);
    }

    if (filters.search) {
      query = query.or(`title.ilike.%${filters.search}%,content.ilike.%${filters.search}%`);
    }

    const limit = filters.limit || 10;
    const offset = ((filters.page || 1) - 1) * limit;

    query = query.range(offset, offset + limit - 1);

    const { data, error } = await query;

    if (error) throw error;
    return data as NewsWithDetails[];
  },

  // 获取单个新闻
  async getNewsById(id: string) {
    if (!isSupabaseConfigured()) {
      return mockNewsApi.getNewsById(id);
    }

    const { data, error } = await supabase
      .from(TABLES.NEWS)
      .select(`
        *,
        category:categories(*),
        author:users(*)
      `)
      .eq('id', id)
      .single();

    if (error) throw error;
    return data as NewsWithDetails;
  },

  // 创建新闻
  async createNews(newsData: CreateNewsData) {
    if (!isSupabaseConfigured()) {
      return mockNewsApi.createNews(newsData);
    }

    const { data, error } = await supabase
      .from(TABLES.NEWS)
      .insert([{
        ...newsData,
        author_id: 'current_user_id', // 这里需要从认证系统获取当前用户ID
        view_count: 0,
        published_at: newsData.status === 'published' ? new Date().toISOString() : null,
      }])
      .select()
      .single();

    if (error) throw error;
    return data as News;
  },

  // 更新新闻
  async updateNews(newsData: UpdateNewsData) {
    if (!isSupabaseConfigured()) {
      return mockNewsApi.updateNews(newsData);
    }

    const { id, ...updateData } = newsData;

    const { data, error } = await supabase
      .from(TABLES.NEWS)
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
        published_at: updateData.status === 'published' ? new Date().toISOString() : undefined,
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data as News;
  },

  // 删除新闻
  async deleteNews(id: string) {
    if (!isSupabaseConfigured()) {
      return mockNewsApi.deleteNews(id);
    }

    const { error } = await supabase
      .from(TABLES.NEWS)
      .delete()
      .eq('id', id);

    if (error) throw error;
  },

  // 增加浏览量
  async incrementViewCount(id: string) {
    if (!isSupabaseConfigured()) {
      return mockNewsApi.incrementViewCount(id);
    }

    const { error } = await supabase.rpc('increment_view_count', { news_id: id });
    if (error) throw error;
  },

  // 批准新闻
  async approveNews(newsId: string) {
    if (!isSupabaseConfigured()) {
      return mockNewsApi.approveNews(newsId);
    }

    const { data, error } = await supabase
      .from(TABLES.NEWS)
      .update({
        status: 'published',
        published_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('id', newsId)
      .select()
      .single();

    if (error) throw error;
    return data as News;
  },

  // 拒绝新闻
  async rejectNews(newsId: string, reason: string) {
    if (!isSupabaseConfigured()) {
      return mockNewsApi.rejectNews(newsId, reason);
    }

    const { data, error } = await supabase
      .from(TABLES.NEWS)
      .update({
        status: 'rejected',
        rejection_reason: reason,
        updated_at: new Date().toISOString(),
      })
      .eq('id', newsId)
      .select()
      .single();

    if (error) throw error;
    return data as News;
  },
};

// 分类相关 API
export const categoryApi = {
  // 获取所有分类
  async getCategories() {
    if (!isSupabaseConfigured()) {
      return mockCategoryApi.getCategories();
    }

    const { data, error } = await supabase
      .from(TABLES.CATEGORIES)
      .select('*')
      .order('name');

    if (error) throw error;
    return data as Category[];
  },

  // 创建分类
  async createCategory(categoryData: Omit<Category, 'id' | 'created_at' | 'updated_at'>) {
    if (!isSupabaseConfigured()) {
      return mockCategoryApi.createCategory(categoryData);
    }

    const { data, error } = await supabase
      .from(TABLES.CATEGORIES)
      .insert([categoryData])
      .select()
      .single();

    if (error) throw error;
    return data as Category;
  },

  // 更新分类
  async updateCategory(id: string, categoryData: Partial<Category>) {
    if (!isSupabaseConfigured()) {
      return mockCategoryApi.updateCategory(id, categoryData);
    }

    const { data, error } = await supabase
      .from(TABLES.CATEGORIES)
      .update({
        ...categoryData,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data as Category;
  },

  // 删除分类
  async deleteCategory(id: string) {
    if (!isSupabaseConfigured()) {
      return mockCategoryApi.deleteCategory(id);
    }

    const { error } = await supabase
      .from(TABLES.CATEGORIES)
      .delete()
      .eq('id', id);

    if (error) throw error;
  },
};

// 用户相关 API
export const userApi = {
  // 获取当前用户
  async getCurrentUser() {
    if (!isSupabaseConfigured()) {
      return mockUserApi.getCurrentUser();
    }

    const { data: { user } } = await supabase.auth.getUser();
    return user;
  },

  // 登录
  async signIn(email: string, password: string) {
    if (!isSupabaseConfigured()) {
      return mockUserApi.signIn(email, password);
    }

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) throw error;
    return data;
  },

  // 登出
  async signOut() {
    if (!isSupabaseConfigured()) {
      return mockUserApi.signOut();
    }

    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  },

  // 获取所有用户（管理员功能）
  async getAllUsers() {
    if (!isSupabaseConfigured()) {
      return mockUserApi.getAllUsers();
    }

    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data as User[];
  },

  // 创建用户（管理员功能）
  async createUser(userData: CreateUserData) {
    if (!isSupabaseConfigured()) {
      return mockUserApi.createUser(userData);
    }

    // 在实际的 Supabase 实现中，这里需要使用 Supabase Auth Admin API
    // 或者通过服务端 API 来创建用户
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .insert([{
        email: userData.email,
        name: userData.name,
        role: userData.role,
        is_active: true,
      }])
      .select()
      .single();

    if (error) throw error;
    return data as User;
  },

  // 更新用户（管理员功能）
  async updateUser(userData: UpdateUserData) {
    if (!isSupabaseConfigured()) {
      return mockUserApi.updateUser(userData);
    }

    const { id, ...updateData } = userData;

    const { data, error } = await supabase
      .from(TABLES.USERS)
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data as User;
  },

  // 删除用户（管理员功能）
  async deleteUser(userId: string) {
    if (!isSupabaseConfigured()) {
      return mockUserApi.deleteUser(userId);
    }

    const { error } = await supabase
      .from(TABLES.USERS)
      .delete()
      .eq('id', userId);

    if (error) throw error;
  },
};
