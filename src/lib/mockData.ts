import type { News, Category, User, CreateNewsData, UpdateNewsData, NewsFilters, NewsWithDetails } from '@/types';
import { newsEvents } from './eventBus';

// 模拟数据
const mockCategories: Category[] = [
  {
    id: '1',
    name: '公司新闻',
    description: '公司内部新闻和公告',
    slug: 'company-news',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: '2',
    name: '行业动态',
    description: '行业相关新闻和趋势',
    slug: 'industry-news',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: '3',
    name: '产品发布',
    description: '新产品发布和更新',
    slug: 'product-releases',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
];

let mockUsers: User[] = [
  {
    id: 'user1',
    email: '<EMAIL>',
    name: '系统管理员',
    role: 'admin',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    is_active: true,
  },
  {
    id: 'user2',
    email: '<EMAIL>',
    name: '编辑',
    role: 'editor',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
    is_active: true,
  },
  {
    id: 'user3',
    email: '<EMAIL>',
    name: '编辑小王',
    role: 'editor',
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
    is_active: true,
  },
];

// 模拟密码存储（实际应用中应该使用加密）
const mockPasswords: Record<string, string> = {
  '<EMAIL>': 'admin123',
  '<EMAIL>': 'editor123',
  '<EMAIL>': 'editor123',
};

let mockNews: NewsWithDetails[] = [
  {
    id: 'news1',
    title: '公司2024年度总结大会成功举办',
    content: '<h2>会议概况</h2><p>2024年12月15日，我公司在总部大楼成功举办了年度总结大会。本次大会回顾了过去一年的重要成就，并对未来发展进行了规划。</p><h3>主要议题</h3><ul><li>2024年业绩回顾</li><li>优秀员工表彰</li><li>2025年发展规划</li></ul><p>会议取得了圆满成功，为公司未来发展奠定了坚实基础。</p>',
    excerpt: '公司年度总结大会成功举办，回顾成就，规划未来发展方向。',
    category_id: '1',
    author_id: 'user1',
    status: 'published',
    published_at: '2024-12-15T10:00:00Z',
    created_at: '2024-12-15T09:00:00Z',
    updated_at: '2024-12-15T09:00:00Z',
    view_count: 156,
    tags: ['年度总结', '会议', '规划'],
    category: mockCategories[0],
    author: mockUsers[0],
  },
  {
    id: 'news2',
    title: '新产品发布：智能办公系统V2.0',
    content: '<h2>产品介绍</h2><p>我们很高兴地宣布，智能办公系统V2.0正式发布！新版本带来了许多令人兴奋的功能和改进。</p><h3>主要新功能</h3><ul><li>AI智能助手</li><li>实时协作编辑</li><li>移动端优化</li><li>数据可视化仪表板</li></ul><p>欢迎所有用户升级体验！</p>',
    excerpt: '智能办公系统V2.0正式发布，带来AI助手、实时协作等新功能。',
    category_id: '3',
    author_id: 'user2',
    status: 'published',
    published_at: '2024-12-10T14:00:00Z',
    created_at: '2024-12-10T13:00:00Z',
    updated_at: '2024-12-10T13:00:00Z',
    view_count: 89,
    tags: ['产品发布', '智能办公', 'AI'],
    category: mockCategories[2],
    author: mockUsers[1],
  },
  {
    id: 'news3',
    title: '行业报告：2024年技术发展趋势分析',
    content: '<h2>报告摘要</h2><p>根据最新的行业调研数据，2024年技术发展呈现出以下几个重要趋势...</p><h3>主要趋势</h3><ol><li>人工智能技术的普及应用</li><li>云计算服务的深度整合</li><li>数据安全重要性日益凸显</li></ol><p>这些趋势将深刻影响未来几年的技术发展方向。</p>',
    excerpt: '深度分析2024年技术发展趋势，包括AI、云计算、数据安全等重点领域。',
    category_id: '2',
    author_id: 'user1',
    status: 'draft',
    created_at: '2024-12-08T16:00:00Z',
    updated_at: '2024-12-08T16:00:00Z',
    view_count: 23,
    tags: ['行业报告', '技术趋势', '分析'],
    category: mockCategories[1],
    author: mockUsers[0],
  },
  {
    id: 'news4',
    title: '新员工培训计划启动通知',
    content: '<h2>培训计划概述</h2><p>为了帮助新员工更好地融入公司文化，我们将启动全新的员工培训计划。</p><h3>培训内容</h3><ul><li>公司文化和价值观</li><li>业务流程介绍</li><li>技能培训</li><li>团队建设活动</li></ul><p>请各部门积极配合，确保培训效果。</p>',
    excerpt: '公司启动新员工培训计划，包含文化介绍、业务流程、技能培训等内容。',
    category_id: '1',
    author_id: 'user2',
    status: 'pending',
    created_at: '2024-12-09T10:00:00Z',
    updated_at: '2024-12-09T10:00:00Z',
    view_count: 0,
    tags: ['培训', '新员工', '通知'],
    category: mockCategories[0],
    author: mockUsers[1],
  },
  {
    id: 'news5',
    title: '技术分享：React 19 新特性解析',
    content: '<h2>React 19 重要更新</h2><p>React 19 带来了许多令人兴奋的新特性和改进...</p><h3>主要特性</h3><ul><li>并发渲染优化</li><li>新的 Hooks API</li><li>服务器组件增强</li><li>性能提升</li></ul><p>让我们一起探索这些新特性如何改善开发体验。</p>',
    excerpt: 'React 19 新特性详细解析，包括并发渲染、新 Hooks、服务器组件等。',
    category_id: '3',
    author_id: 'user3',
    status: 'published',
    published_at: '2024-12-09T15:00:00Z',
    created_at: '2024-12-09T14:00:00Z',
    updated_at: '2024-12-09T15:00:00Z',
    view_count: 45,
    tags: ['React', '技术分享', '前端'],
    category: mockCategories[2],
    author: mockUsers[2],
  },
  {
    id: 'news6',
    title: '荣联科技荣获"年度最佳创新企业"奖项',
    content: '<h2>获奖详情</h2><p>在2024年度科技创新大会上，荣联科技凭借在人工智能和云计算领域的突出贡献，荣获"年度最佳创新企业"奖项。</p><h3>获奖理由</h3><ul><li>技术创新能力突出</li><li>产品市场表现优异</li><li>社会责任履行到位</li></ul><p>这一荣誉是对我们团队努力的最好认可。</p>',
    excerpt: '荣联科技在2024年度科技创新大会上荣获"年度最佳创新企业"奖项。',
    category_id: '1',
    author_id: 'user1',
    status: 'published',
    published_at: '2024-12-07T09:00:00Z',
    created_at: '2024-12-07T08:00:00Z',
    updated_at: '2024-12-07T09:00:00Z',
    view_count: 234,
    tags: ['获奖', '创新', '企业荣誉'],
    category: mockCategories[0],
    author: mockUsers[0],
  },
  {
    id: 'news7',
    title: '云计算服务升级：性能提升50%',
    content: '<h2>升级内容</h2><p>经过三个月的技术攻关，我们的云计算服务平台完成了重大升级。</p><h3>主要改进</h3><ul><li>计算性能提升50%</li><li>存储容量扩大一倍</li><li>网络延迟降低30%</li><li>新增AI加速功能</li></ul><p>升级后的服务将为客户提供更好的使用体验。</p>',
    excerpt: '云计算服务平台完成重大升级，性能提升50%，存储容量扩大一倍。',
    category_id: '3',
    author_id: 'user2',
    status: 'published',
    published_at: '2024-12-06T16:00:00Z',
    created_at: '2024-12-06T15:00:00Z',
    updated_at: '2024-12-06T16:00:00Z',
    view_count: 178,
    tags: ['云计算', '升级', '性能'],
    category: mockCategories[2],
    author: mockUsers[1],
  },
];

// 模拟 API 延迟
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// 模拟新闻 API
export const mockNewsApi = {
  async getNews(filters: NewsFilters = {}) {
    await delay(500); // 模拟网络延迟
    
    let filteredNews = [...mockNews];
    
    if (filters.category_id) {
      filteredNews = filteredNews.filter(news => news.category_id === filters.category_id);
    }
    
    if (filters.status) {
      filteredNews = filteredNews.filter(news => news.status === filters.status);
    }
    
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filteredNews = filteredNews.filter(news => 
        news.title.toLowerCase().includes(searchLower) ||
        news.content.toLowerCase().includes(searchLower)
      );
    }
    
    // 简单分页
    const limit = filters.limit || 10;
    const offset = ((filters.page || 1) - 1) * limit;
    
    return filteredNews.slice(offset, offset + limit);
  },

  async getNewsById(id: string) {
    await delay(300);
    const news = mockNews.find(n => n.id === id);
    if (!news) throw new Error('新闻不存在');
    return news;
  },

  async createNews(newsData: CreateNewsData) {
    await delay(800);

    // 根据用户角色决定新闻状态
    let finalStatus = newsData.status;
    if (currentUser?.role === 'editor' && newsData.status === 'published') {
      finalStatus = 'pending'; // 编辑提交发布申请，状态改为待审批
    }

    const newNews: NewsWithDetails = {
      id: `news${Date.now()}`,
      ...newsData,
      status: finalStatus,
      author_id: currentUser?.id || 'user1',
      view_count: 0,
      published_at: finalStatus === 'published' ? new Date().toISOString() : undefined,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      category: mockCategories.find(c => c.id === newsData.category_id) || mockCategories[0],
      author: currentUser || mockUsers[0],
    };
    mockNews.unshift(newNews);

    // 触发新闻创建事件
    newsEvents.notifyNewsCreated(newNews);

    return newNews;
  },

  async updateNews(newsData: UpdateNewsData) {
    await delay(800);
    const index = mockNews.findIndex(n => n.id === newsData.id);
    if (index === -1) throw new Error('新闻不存在');
    
    const { id, ...updateData } = newsData;
    mockNews[index] = {
      ...mockNews[index],
      ...updateData,
      updated_at: new Date().toISOString(),
      published_at: updateData.status === 'published' ? new Date().toISOString() : mockNews[index].published_at,
      category: updateData.category_id ? 
        mockCategories.find(c => c.id === updateData.category_id) || mockNews[index].category :
        mockNews[index].category,
    };

    // 触发新闻更新事件
    newsEvents.notifyNewsUpdated(mockNews[index]);

    return mockNews[index];
  },

  async deleteNews(id: string) {
    await delay(500);
    const index = mockNews.findIndex(n => n.id === id);
    if (index === -1) throw new Error('新闻不存在');
    mockNews.splice(index, 1);

    // 触发新闻删除事件
    newsEvents.notifyNewsDeleted(id);
  },

  async incrementViewCount(id: string) {
    await delay(200);
    const news = mockNews.find(n => n.id === id);
    if (news) {
      news.view_count += 1;
    }
  },

  async approveNews(newsId: string) {
    await delay(500);
    const index = mockNews.findIndex(n => n.id === newsId);
    if (index === -1) throw new Error('新闻不存在');

    mockNews[index] = {
      ...mockNews[index],
      status: 'published',
      published_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // 触发新闻审批事件
    newsEvents.notifyNewsApproved(mockNews[index]);

    return mockNews[index];
  },

  async rejectNews(newsId: string, reason: string) {
    await delay(500);
    const index = mockNews.findIndex(n => n.id === newsId);
    if (index === -1) throw new Error('新闻不存在');

    mockNews[index] = {
      ...mockNews[index],
      status: 'rejected',
      rejection_reason: reason,
      updated_at: new Date().toISOString(),
    };
    return mockNews[index];
  },
};

// 模拟分类 API
export const mockCategoryApi = {
  async getCategories() {
    await delay(300);
    return [...mockCategories];
  },

  async createCategory(categoryData: Omit<Category, 'id' | 'created_at' | 'updated_at'>) {
    await delay(500);
    const newCategory: Category = {
      id: `cat${Date.now()}`,
      ...categoryData,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    mockCategories.push(newCategory);
    return newCategory;
  },

  async updateCategory(id: string, categoryData: Partial<Category>) {
    await delay(500);
    const index = mockCategories.findIndex(c => c.id === id);
    if (index === -1) throw new Error('分类不存在');
    
    mockCategories[index] = {
      ...mockCategories[index],
      ...categoryData,
      updated_at: new Date().toISOString(),
    };
    return mockCategories[index];
  },

  async deleteCategory(id: string) {
    await delay(500);
    const index = mockCategories.findIndex(c => c.id === id);
    if (index === -1) throw new Error('分类不存在');
    mockCategories.splice(index, 1);
  },
};

// 当前登录用户（模拟会话）
let currentUser: User | null = null;

// 导出获取当前用户的函数（用于调试）
export const getCurrentUser = () => currentUser;

// 模拟用户 API
export const mockUserApi = {
  async getCurrentUser() {
    await delay(200);
    return currentUser;
  },

  async signIn(email: string, password: string) {
    await delay(1000);
    const user = mockUsers.find(u => u.email === email && u.is_active !== false);
    if (!user) throw new Error('用户不存在或已被禁用');

    const storedPassword = mockPasswords[email];
    if (!storedPassword || storedPassword !== password) {
      throw new Error('密码错误');
    }

    currentUser = user;
    return { user };
  },

  async signOut() {
    await delay(300);
    currentUser = null;
  },

  async getAllUsers() {
    await delay(500);
    return [...mockUsers];
  },

  async createUser(userData: { email: string; name: string; password: string; role: 'admin' | 'editor' }) {
    await delay(800);

    // 检查邮箱是否已存在
    if (mockUsers.find(u => u.email === userData.email)) {
      throw new Error('邮箱已存在');
    }

    const newUser: User = {
      id: `user${Date.now()}`,
      email: userData.email,
      name: userData.name,
      role: userData.role,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_active: true,
    };

    mockUsers.push(newUser);
    mockPasswords[userData.email] = userData.password;

    return newUser;
  },

  async updateUser(userData: { id: string; name?: string; role?: 'admin' | 'editor'; is_active?: boolean }) {
    await delay(500);

    const index = mockUsers.findIndex(u => u.id === userData.id);
    if (index === -1) throw new Error('用户不存在');

    mockUsers[index] = {
      ...mockUsers[index],
      ...userData,
      updated_at: new Date().toISOString(),
    };

    return mockUsers[index];
  },

  async deleteUser(userId: string) {
    await delay(500);

    const index = mockUsers.findIndex(u => u.id === userId);
    if (index === -1) throw new Error('用户不存在');

    const user = mockUsers[index];
    mockUsers.splice(index, 1);
    delete mockPasswords[user.email];
  },
};
