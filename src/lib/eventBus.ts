// 全局事件总线，用于组件间通信
type EventCallback = (...args: any[]) => void;

class EventBus {
  private events: { [key: string]: EventCallback[] } = {};

  // 订阅事件
  on(event: string, callback: EventCallback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
  }

  // 取消订阅
  off(event: string, callback: EventCallback) {
    if (!this.events[event]) return;
    this.events[event] = this.events[event].filter(cb => cb !== callback);
  }

  // 触发事件
  emit(event: string, ...args: any[]) {
    if (!this.events[event]) return;
    this.events[event].forEach(callback => {
      try {
        callback(...args);
      } catch (error) {
        console.error(`Error in event callback for ${event}:`, error);
      }
    });
  }

  // 清除所有事件监听器
  clear() {
    this.events = {};
  }
}

// 创建全局事件总线实例
export const eventBus = new EventBus();

// 定义事件类型
export const EVENTS = {
  NEWS_CREATED: 'news:created',
  NEWS_UPDATED: 'news:updated',
  NEWS_DELETED: 'news:deleted',
  NEWS_APPROVED: 'news:approved',
  NEWS_REFRESH_NEEDED: 'news:refresh_needed',
} as const;

// 新闻相关事件的辅助函数
export const newsEvents = {
  // 通知新闻已创建
  notifyNewsCreated: (news: any) => {
    console.log('事件总线: 新闻已创建', news);
    eventBus.emit(EVENTS.NEWS_CREATED, news);
    eventBus.emit(EVENTS.NEWS_REFRESH_NEEDED);
  },

  // 通知新闻已更新
  notifyNewsUpdated: (news: any) => {
    console.log('事件总线: 新闻已更新', news);
    eventBus.emit(EVENTS.NEWS_UPDATED, news);
    eventBus.emit(EVENTS.NEWS_REFRESH_NEEDED);
  },

  // 通知新闻已删除
  notifyNewsDeleted: (newsId: string) => {
    console.log('事件总线: 新闻已删除', newsId);
    eventBus.emit(EVENTS.NEWS_DELETED, newsId);
    eventBus.emit(EVENTS.NEWS_REFRESH_NEEDED);
  },

  // 通知新闻已审批
  notifyNewsApproved: (news: any) => {
    console.log('事件总线: 新闻已审批', news);
    eventBus.emit(EVENTS.NEWS_APPROVED, news);
    eventBus.emit(EVENTS.NEWS_REFRESH_NEEDED);
  },

  // 订阅新闻刷新事件
  onRefreshNeeded: (callback: EventCallback) => {
    eventBus.on(EVENTS.NEWS_REFRESH_NEEDED, callback);
  },

  // 取消订阅新闻刷新事件
  offRefreshNeeded: (callback: EventCallback) => {
    eventBus.off(EVENTS.NEWS_REFRESH_NEEDED, callback);
  },
};
