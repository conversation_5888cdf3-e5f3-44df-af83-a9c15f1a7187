import { createApp } from 'vue'

console.log('🚀 开始创建最简单的 Vue 3 应用 (JavaScript版本)...')

// 创建一个最简单的Vue应用
const SimpleApp = {
  template: `
    <div style="padding: 20px; background: lightgreen; margin: 10px; border: 2px solid green;">
      <h1 style="color: green;">🎉 Vue 3 JavaScript 应用成功运行！</h1>
      <p>当前时间: {{ currentTime }}</p>
      <button @click="updateTime" style="background: green; color: white; padding: 10px; border: none; cursor: pointer; margin: 10px;">
        更新时间
      </button>
      <p>计数器: {{ count }}</p>
      <button @click="increment" style="background: blue; color: white; padding: 10px; border: none; cursor: pointer;">
        点击 +1
      </button>
      <div style="margin-top: 20px; padding: 10px; background: white; border: 1px solid gray;">
        <h3>测试结果:</h3>
        <p>✅ Vue 3 基础功能正常</p>
        <p>✅ 响应式数据绑定正常</p>
        <p>✅ 事件处理正常</p>
      </div>
    </div>
  `,
  data() {
    return {
      currentTime: new Date().toLocaleString(),
      count: 0
    }
  },
  methods: {
    updateTime() {
      this.currentTime = new Date().toLocaleString()
      console.log('时间已更新:', this.currentTime)
    },
    increment() {
      this.count++
      console.log('计数器:', this.count)
    }
  },
  mounted() {
    console.log('✅ Vue 3 JavaScript 应用已成功挂载！')
  }
}

try {
  const app = createApp(SimpleApp)
  console.log('✅ Vue 应用创建成功')

  app.mount('#app')
  console.log('🎉 Vue 应用已成功挂载到 #app')
} catch (error) {
  console.error('❌ Vue 应用启动失败:', error)
  
  // 显示错误信息
  const appElement = document.getElementById('app')
  if (appElement) {
    appElement.innerHTML = `
      <div style="padding: 20px; background: red; color: white; margin: 10px;">
        <h2>❌ Vue 3 应用启动失败</h2>
        <p>错误信息: ${error}</p>
        <p>请检查浏览器控制台获取详细信息。</p>
      </div>
    `
  }
}
