export interface News {
  id: string;
  title: string;
  content: string;
  excerpt: string;
  category_id: string;
  author_id: string;
  status: 'draft' | 'pending' | 'published' | 'archived' | 'rejected';
  featured_image?: string;
  published_at?: string;
  created_at: string;
  updated_at: string;
  view_count: number;
  tags?: string[];
  rejection_reason?: string;
}

export interface Category {
  id: string;
  name: string;
  description?: string;
  slug: string;
  created_at: string;
  updated_at: string;
}

export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'editor';
  avatar_url?: string;
  created_at: string;
  updated_at: string;
  is_active?: boolean;
}

export interface NewsWithDetails extends News {
  category: Category;
  author: User;
}

export interface CreateNewsData {
  title: string;
  content: string;
  excerpt: string;
  category_id: string;
  status: 'draft' | 'pending' | 'published';
  featured_image?: string;
  tags?: string[];
}

export interface UpdateNewsData extends Partial<CreateNewsData> {
  id: string;
}

export interface NewsFilters {
  category_id?: string;
  status?: string;
  search?: string;
  page?: number;
  limit?: number;
  author_id?: string;
}

// 用户相关类型
export interface CreateUserData {
  email: string;
  name: string;
  password: string;
  role: 'admin' | 'editor';
}

export interface UpdateUserData {
  id: string;
  name?: string;
  role?: 'admin' | 'editor';
  is_active?: boolean;
}

export interface LoginData {
  email: string;
  password: string;
}

// 新闻审批相关类型
export interface ApprovalAction {
  newsId: string;
  action: 'approve' | 'reject';
  reason?: string;
}

// API 响应类型
export interface ApiResponse<T> {
  data: T;
  error?: string;
}

// 分页响应类型
export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
