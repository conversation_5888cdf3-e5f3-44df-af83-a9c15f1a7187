import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { newsApi } from '@/lib/vue-api'
import type { NewsWithDetails, NewsFilters, CreateNewsData, UpdateNewsData } from '@/types/vue-types'

export const useNewsStore = defineStore('news', () => {
  // State
  const news = ref<NewsWithDetails[]>([])
  const currentNews = ref<NewsWithDetails | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)
  const filters = ref<NewsFilters>({
    page: 1,
    limit: 50,
  })

  // Getters
  const publishedNews = computed(() => 
    news.value.filter(item => item.status === 'published')
  )
  
  const pendingNews = computed(() => 
    news.value.filter(item => item.status === 'pending')
  )
  
  const totalNews = computed(() => news.value.length)

  // Actions
  const fetchNews = async (newFilters?: Partial<NewsFilters>) => {
    try {
      loading.value = true
      error.value = null
      
      if (newFilters) {
        filters.value = { ...filters.value, ...newFilters }
      }
      
      const data = await newsApi.getNews(filters.value)
      news.value = data
      
      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取新闻失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const fetchNewsById = async (id: string) => {
    try {
      loading.value = true
      error.value = null
      
      const data = await newsApi.getNewsById(id)
      currentNews.value = data
      
      return data
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取新闻详情失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const createNews = async (newsData: CreateNewsData) => {
    try {
      loading.value = true
      error.value = null
      
      const newNews = await newsApi.createNews(newsData)
      
      // 添加到列表开头
      news.value.unshift(newNews as NewsWithDetails)
      
      return newNews
    } catch (err) {
      error.value = err instanceof Error ? err.message : '创建新闻失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateNews = async (newsData: UpdateNewsData) => {
    try {
      loading.value = true
      error.value = null
      
      const updatedNews = await newsApi.updateNews(newsData)
      
      // 更新列表中的新闻
      const index = news.value.findIndex(item => item.id === newsData.id)
      if (index !== -1) {
        news.value[index] = updatedNews as NewsWithDetails
      }
      
      // 如果是当前查看的新闻，也更新
      if (currentNews.value?.id === newsData.id) {
        currentNews.value = updatedNews as NewsWithDetails
      }
      
      return updatedNews
    } catch (err) {
      error.value = err instanceof Error ? err.message : '更新新闻失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const deleteNews = async (id: string) => {
    try {
      loading.value = true
      error.value = null
      
      await newsApi.deleteNews(id)
      
      // 从列表中移除
      news.value = news.value.filter(item => item.id !== id)
      
      // 如果是当前查看的新闻，清空
      if (currentNews.value?.id === id) {
        currentNews.value = null
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '删除新闻失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const approveNews = async (newsId: string) => {
    try {
      loading.value = true
      error.value = null
      
      const approvedNews = await newsApi.approveNews(newsId)
      
      // 更新列表中的新闻状态
      const index = news.value.findIndex(item => item.id === newsId)
      if (index !== -1) {
        news.value[index] = approvedNews as NewsWithDetails
      }
      
      return approvedNews
    } catch (err) {
      error.value = err instanceof Error ? err.message : '审批新闻失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const rejectNews = async (newsId: string, reason: string) => {
    try {
      loading.value = true
      error.value = null
      
      const rejectedNews = await newsApi.rejectNews(newsId, reason)
      
      // 更新列表中的新闻状态
      const index = news.value.findIndex(item => item.id === newsId)
      if (index !== -1) {
        news.value[index] = rejectedNews as NewsWithDetails
      }
      
      return rejectedNews
    } catch (err) {
      error.value = err instanceof Error ? err.message : '拒绝新闻失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const incrementViewCount = async (id: string) => {
    try {
      await newsApi.incrementViewCount(id)
      
      // 更新本地数据
      const newsItem = news.value.find(item => item.id === id)
      if (newsItem) {
        newsItem.view_count += 1
      }
      
      if (currentNews.value?.id === id) {
        currentNews.value.view_count += 1
      }
    } catch (err) {
      // 浏览量更新失败不影响用户体验，只记录错误
      console.error('更新浏览量失败:', err)
    }
  }

  const clearError = () => {
    error.value = null
  }

  const clearCurrentNews = () => {
    currentNews.value = null
  }

  return {
    // State
    news,
    currentNews,
    loading,
    error,
    filters,
    
    // Getters
    publishedNews,
    pendingNews,
    totalNews,
    
    // Actions
    fetchNews,
    fetchNewsById,
    createNews,
    updateNews,
    deleteNews,
    approveNews,
    rejectNews,
    incrementViewCount,
    clearError,
    clearCurrentNews,
  }
})
