import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 简化的用户类型
interface User {
  id: string
  name: string
  email: string
  role: 'admin' | 'editor'
}

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const isAuthenticated = computed(() => !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isEditor = computed(() => user.value?.role === 'editor' || user.value?.role === 'admin')

  // 模拟用户数据
  const mockUsers = [
    {
      id: 'user1',
      name: '管理员',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin' as const
    },
    {
      id: 'user2',
      name: '编辑',
      email: '<EMAIL>',
      password: 'editor123',
      role: 'editor' as const
    }
  ]

  // Actions
  const login = async (email: string, password: string) => {
    loading.value = true
    error.value = null

    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 查找用户
      const foundUser = mockUsers.find(u => u.email === email && u.password === password)

      if (!foundUser) {
        throw new Error('用户名或密码错误')
      }

      // 移除密码字段
      const { password: _, ...userData } = foundUser
      user.value = userData

      // 保存到 localStorage
      localStorage.setItem('auth_user', JSON.stringify(userData))

      console.log('✅ 登录成功:', userData.name, userData.role)
      return userData
    } catch (err) {
      error.value = err instanceof Error ? err.message : '登录失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const logout = () => {
    user.value = null
    error.value = null
    localStorage.removeItem('auth_user')
    console.log('✅ 用户已登出')
  }

  const initializeAuth = async () => {
    // 从 localStorage 恢复用户状态
    const savedUser = localStorage.getItem('auth_user')
    if (savedUser) {
      try {
        user.value = JSON.parse(savedUser)
        console.log('✅ 用户状态已恢复:', user.value?.name)
      } catch (err) {
        console.error('❌ 恢复用户状态失败:', err)
        localStorage.removeItem('auth_user')
      }
    }
  }

  return {
    // State
    user,
    loading,
    error,

    // Getters
    isAuthenticated,
    isAdmin,
    isEditor,

    // Actions
    login,
    logout,
    initializeAuth,
  }
})
