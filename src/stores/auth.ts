import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { userApi } from '@/lib/api'
import type { User, LoginData } from '@/types/vue-types'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<User | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const isAuthenticated = computed(() => !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isEditor = computed(() => user.value?.role === 'editor')

  // Actions
  const initializeAuth = async () => {
    try {
      loading.value = true
      const currentUser = await userApi.getCurrentUser()
      user.value = currentUser
    } catch (err) {
      // 用户未登录，这是正常情况
      user.value = null
    } finally {
      loading.value = false
    }
  }

  const login = async (credentials: LoginData) => {
    try {
      loading.value = true
      error.value = null
      
      const response = await userApi.signIn(credentials.email, credentials.password)
      user.value = response.user
      
      return response
    } catch (err) {
      error.value = err instanceof Error ? err.message : '登录失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const logout = async () => {
    try {
      loading.value = true
      await userApi.signOut()
      user.value = null
      error.value = null
    } catch (err) {
      error.value = err instanceof Error ? err.message : '登出失败'
      throw err
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // State
    user,
    loading,
    error,
    
    // Getters
    isAuthenticated,
    isAdmin,
    isEditor,
    
    // Actions
    initializeAuth,
    login,
    logout,
    clearError,
  }
})
