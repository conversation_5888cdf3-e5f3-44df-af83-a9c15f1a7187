import { createApp } from 'vue'
import { createPinia } from 'pinia'
import router from './router'
import App from './App.vue'
import './style.css'

console.log('🚀 开始创建完整的 Vue 3 应用...')

try {
  const app = createApp(App)
  console.log('✅ Vue 应用创建成功')

  app.use(createPinia())
  console.log('✅ Pinia 状态管理已安装')

  app.use(router)
  console.log('✅ Vue Router 已安装')

  app.mount('#app')
  console.log('🎉 Vue 应用已成功挂载到 #app')
} catch (error) {
  console.error('❌ Vue 应用启动失败:', error)

  // 显示错误信息
  const appElement = document.getElementById('app')
  if (appElement) {
    appElement.innerHTML = `
      <div style="padding: 20px; background: red; color: white; margin: 10px;">
        <h2>❌ Vue 3 应用启动失败</h2>
        <p>错误信息: ${error}</p>
        <p>请检查浏览器控制台获取详细信息。</p>
      </div>
    `
  }
}
