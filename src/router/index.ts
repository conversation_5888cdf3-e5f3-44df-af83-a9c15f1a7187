import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('@/views/Home.vue'),
    },
    {
      path: '/admin',
      name: 'Admin',
      component: () => import('@/views/Admin.vue'),
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          redirect: '/admin/news'
        },
        {
          path: 'news',
          name: 'AdminNews',
          component: () => import('@/views/admin/NewsManagement.vue'),
        },
        {
          path: 'news/create',
          name: 'CreateNews',
          component: () => import('@/views/admin/NewsForm.vue'),
        },
        {
          path: 'news/:id/edit',
          name: 'EditNews',
          component: () => import('@/views/admin/NewsForm.vue'),
          props: true,
        },
        {
          path: 'approval',
          name: 'NewsApproval',
          component: () => import('@/views/admin/NewsApproval.vue'),
          meta: { requiresAdmin: true },
        },
        {
          path: 'users',
          name: 'UserManagement',
          component: () => import('@/views/admin/UserManagement.vue'),
          meta: { requiresAdmin: true },
        },
      ],
    },
    {
      path: '/news/:id',
      name: 'NewsDetail',
      component: () => import('@/views/NewsDetail.vue'),
      props: true,
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('@/views/Login.vue'),
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('@/views/NotFound.vue'),
    },
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  // 检查是否需要认证
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
    return
  }
  
  // 检查是否需要管理员权限
  if (to.meta.requiresAdmin && !authStore.isAdmin) {
    next('/admin')
    return
  }
  
  // 如果已登录用户访问登录页，重定向到管理页面
  if (to.name === 'Login' && authStore.isAuthenticated) {
    next('/admin')
    return
  }
  
  next()
})

export default router
