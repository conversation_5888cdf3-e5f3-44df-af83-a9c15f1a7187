import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('@/views/Home.vue'),
    },
    {
      path: '/admin',
      name: 'Admin',
      component: () => import('@/views/Admin.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/admin/news',
      name: 'AdminNews',
      component: () => import('@/views/admin/NewsManagement.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/admin/news/create',
      name: 'AdminNewsCreate',
      component: () => import('@/views/admin/NewsCreate.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/admin/news/edit/:id',
      name: 'AdminNewsEdit',
      component: () => import('@/views/admin/NewsEdit.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/admin/news/approval',
      name: 'AdminNewsApproval',
      component: () => import('@/views/admin/NewsApproval.vue'),
      meta: { requiresAuth: true, requiresAdmin: true },
    },
    {
      path: '/admin/users',
      name: 'AdminUsers',
      component: () => import('@/views/admin/UserManagement.vue'),
      meta: { requiresAuth: true, requiresAdmin: true },
    },
    {
      path: '/admin/categories',
      name: 'AdminCategories',
      component: () => import('@/views/admin/CategoryManagement.vue'),
      meta: { requiresAuth: true, requiresAdmin: true },
    },
    {
      path: '/admin/analytics',
      name: 'AdminAnalytics',
      component: () => import('@/views/admin/DataAnalytics.vue'),
      meta: { requiresAuth: true, requiresAdmin: true },
    },
    {
      path: '/admin/settings',
      name: 'AdminSettings',
      component: () => import('@/views/admin/SystemSettings.vue'),
      meta: { requiresAuth: true, requiresAdmin: true },
    },
    {
      path: '/admin/logs',
      name: 'AdminLogs',
      component: () => import('@/views/admin/LogManagement.vue'),
      meta: { requiresAuth: true, requiresAdmin: true },
    },
    {
      path: '/admin/api-docs',
      name: 'AdminApiDocs',
      component: () => import('@/views/admin/ApiDocumentation.vue'),
      meta: { requiresAuth: true, requiresAdmin: true },
    },
    {
      path: '/test',
      name: 'Test',
      component: () => {
        return Promise.resolve({
          template: `
            <div style="padding: 20px; background: yellow; margin: 10px; border: 2px solid orange;">
              <h2 style="color: orange;">🧪 测试路由页面</h2>
              <p>这是一个测试路由，用于验证路由系统正常工作</p>
            </div>
          `
        })
      }
    }
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  console.log('🚀 路由导航:', from.path, '->', to.path)

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    const authStore = useAuthStore()
    if (!authStore.isAuthenticated) {
      console.log('❌ 需要登录，重定向到首页')
      next('/')
      return
    }
  }

  next()
})

export default router
