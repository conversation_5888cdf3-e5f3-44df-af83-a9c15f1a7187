import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('@/views/Home.vue'),
    },
    {
      path: '/admin',
      name: 'Admin',
      component: () => import('@/views/Admin.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/admin/news',
      name: 'AdminNews',
      component: () => import('@/views/admin/NewsManagement.vue'),
      meta: { requiresAuth: true },
    },
    {
      path: '/test',
      name: 'Test',
      component: () => {
        return Promise.resolve({
          template: `
            <div style="padding: 20px; background: yellow; margin: 10px; border: 2px solid orange;">
              <h2 style="color: orange;">🧪 测试路由页面</h2>
              <p>这是一个测试路由，用于验证路由系统正常工作</p>
            </div>
          `
        })
      }
    }
  ],
})

// 路由守卫
router.beforeEach((to, from, next) => {
  console.log('🚀 路由导航:', from.path, '->', to.path)

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    const authStore = useAuthStore()
    if (!authStore.isAuthenticated) {
      console.log('❌ 需要登录，重定向到首页')
      next('/')
      return
    }
  }

  next()
})

export default router
