<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="handleBackdropClick">
    <div class="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden" @click.stop>
      <!-- 头部 -->
      <div class="flex justify-between items-center p-6 border-b border-gray-200">
        <div class="flex-1">
          <h2 class="text-xl font-bold text-gray-900 mb-2">{{ news?.title }}</h2>
          <div class="flex items-center space-x-4 text-sm text-gray-500">
            <span class="flex items-center">
              <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              {{ news?.author.name }}
            </span>
            <span class="flex items-center">
              <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
              {{ news?.category.name }}
            </span>
            <span class="flex items-center">
              <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              {{ news?.view_count }} 次浏览
            </span>
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
              :class="getStatusClass(news?.status)"
            >
              {{ getStatusText(news?.status) }}
            </span>
          </div>
        </div>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600 ml-4">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 内容 -->
      <div class="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
        <!-- 摘要 -->
        <div v-if="news?.excerpt" class="mb-6 p-4 bg-gray-50 rounded-lg">
          <h3 class="text-sm font-medium text-gray-700 mb-2">摘要</h3>
          <p class="text-gray-600">{{ news.excerpt }}</p>
        </div>

        <!-- 正文内容 -->
        <div class="prose max-w-none">
          <div v-html="news?.content"></div>
        </div>

        <!-- 标签 -->
        <div v-if="news?.tags && news.tags.length > 0" class="mt-6 pt-6 border-t border-gray-200">
          <h3 class="text-sm font-medium text-gray-700 mb-2">标签</h3>
          <div class="flex flex-wrap gap-2">
            <span
              v-for="tag in news.tags"
              :key="tag"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
            >
              {{ tag }}
            </span>
          </div>
        </div>
      </div>

      <!-- 底部信息 -->
      <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
        <div class="flex justify-between items-center text-sm text-gray-500">
          <div>
            <span>创建时间: {{ formatDate(news?.created_at) }}</span>
            <span v-if="news?.updated_at && news.updated_at !== news.created_at" class="ml-4">
              最后修改: {{ formatDate(news?.updated_at) }}
            </span>
          </div>
          <div v-if="news?.published_at">
            发布时间: {{ formatDate(news.published_at) }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Props
interface Props {
  news?: any
}

defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

// 方法
const handleBackdropClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    emit('close')
  }
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'published':
      return 'bg-green-100 text-green-800'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'draft':
      return 'bg-gray-100 text-gray-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'published':
      return '已发布'
    case 'pending':
      return '待审批'
    case 'draft':
      return '草稿'
    default:
      return '未知'
  }
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}
</script>

<style scoped>
/* 确保新闻内容样式正确显示 */
:deep(.prose) {
  color: #374151;
  line-height: 1.75;
}

:deep(.prose h1) {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

:deep(.prose h2) {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
}

:deep(.prose h3) {
  font-size: 1.125rem;
  font-weight: 700;
  color: #111827;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

:deep(.prose p) {
  margin-bottom: 1rem;
  line-height: 1.75;
}

:deep(.prose ul) {
  list-style-type: disc;
  list-style-position: inside;
  margin-bottom: 1rem;
}

:deep(.prose ol) {
  list-style-type: decimal;
  list-style-position: inside;
  margin-bottom: 1rem;
}

:deep(.prose li) {
  line-height: 1.75;
  margin-bottom: 0.25rem;
}

:deep(.prose blockquote) {
  border-left: 4px solid #3b82f6;
  padding-left: 1rem;
  font-style: italic;
  color: #4b5563;
  margin: 1rem 0;
}

:deep(.prose strong) {
  font-weight: 600;
  color: #111827;
}
</style>
