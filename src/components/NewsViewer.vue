<template>
  <TransitionRoot appear :show="true" as="template">
    <Dialog as="div" class="relative z-50" @close="$emit('close')">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-50" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel class="w-full max-w-4xl transform overflow-hidden rounded-2xl bg-white shadow-xl transition-all">
              <!-- 头部 -->
              <div class="bg-white px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                      <NewspaperIcon class="h-5 w-5 text-white" />
                    </div>
                    <h2 class="text-lg font-semibold text-gray-900">新闻详情</h2>
                  </div>
                  <button
                    @click="$emit('close')"
                    class="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <XMarkIcon class="h-6 w-6" />
                  </button>
                </div>
              </div>

              <!-- 内容 -->
              <div class="max-h-[80vh] overflow-y-auto">
                <article class="px-6 py-6">
                  <!-- 标题 -->
                  <h1 class="text-3xl font-bold text-gray-900 mb-6">
                    {{ news.title }}
                  </h1>

                  <!-- 元信息 -->
                  <div class="flex flex-wrap items-center gap-6 mb-6 text-sm text-gray-600">
                    <div class="flex items-center">
                      <FolderIcon class="h-4 w-4 mr-2" />
                      <span>{{ news.category.name }}</span>
                    </div>
                    <div class="flex items-center">
                      <UserIcon class="h-4 w-4 mr-2" />
                      <span>{{ news.author.name }}</span>
                    </div>
                    <div class="flex items-center">
                      <CalendarIcon class="h-4 w-4 mr-2" />
                      <span>{{ formatDate(news.published_at || news.created_at) }}</span>
                    </div>
                    <div class="flex items-center">
                      <EyeIcon class="h-4 w-4 mr-2" />
                      <span>{{ news.view_count }} 次浏览</span>
                    </div>
                  </div>

                  <!-- 标签 -->
                  <div v-if="news.tags && news.tags.length > 0" class="mb-6">
                    <div class="flex flex-wrap gap-2">
                      <span
                        v-for="(tag, index) in news.tags"
                        :key="index"
                        class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
                      >
                        {{ tag }}
                      </span>
                    </div>
                  </div>

                  <!-- 摘要 -->
                  <div v-if="news.excerpt" class="mb-6 p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500">
                    <p class="text-gray-700 italic">{{ news.excerpt }}</p>
                  </div>

                  <!-- 正文内容 -->
                  <div class="prose max-w-none" v-html="news.content"></div>
                </article>
              </div>

              <!-- 底部 -->
              <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
                <div class="flex justify-between items-center">
                  <div class="text-sm text-gray-500">
                    最后更新: {{ formatDateTime(news.updated_at) }}
                  </div>
                  <button
                    @click="$emit('close')"
                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    关闭
                  </button>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import {
  Dialog,
  DialogPanel,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import {
  XMarkIcon,
  NewspaperIcon,
  FolderIcon,
  UserIcon,
  CalendarIcon,
  EyeIcon,
} from '@heroicons/vue/24/outline'
import type { NewsWithDetails } from '@/types/vue-types'

// Props & Emits
interface Props {
  news: NewsWithDetails
}

defineProps<Props>()

defineEmits<{
  close: []
}>()

// Methods
const formatDate = (dateString: string) => {
  return format(new Date(dateString), 'yyyy年MM月dd日', { locale: zhCN })
}

const formatDateTime = (dateString: string) => {
  return format(new Date(dateString), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })
}
</script>

<style scoped>
/* 确保新闻内容样式正确显示 */
:deep(.prose) {
  color: #374151;
  line-height: 1.75;
}

:deep(.prose h1) {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
}

:deep(.prose h2) {
  font-size: 1.25rem;
  font-weight: 700;
  color: #111827;
  margin-top: 1.25rem;
  margin-bottom: 0.75rem;
}

:deep(.prose h3) {
  font-size: 1.125rem;
  font-weight: 700;
  color: #111827;
  margin-top: 1rem;
  margin-bottom: 0.5rem;
}

:deep(.prose p) {
  margin-bottom: 1rem;
  line-height: 1.75;
}

:deep(.prose ul) {
  list-style-type: disc;
  list-style-position: inside;
  margin-bottom: 1rem;
}

:deep(.prose ol) {
  list-style-type: decimal;
  list-style-position: inside;
  margin-bottom: 1rem;
}

:deep(.prose li) {
  line-height: 1.75;
  margin-bottom: 0.25rem;
}

:deep(.prose blockquote) {
  border-left: 4px solid #3b82f6;
  padding-left: 1rem;
  font-style: italic;
  color: #4b5563;
  margin: 1rem 0;
}

:deep(.prose strong) {
  font-weight: 600;
  color: #111827;
}
</style>
