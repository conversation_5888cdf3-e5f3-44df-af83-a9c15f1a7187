<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="handleBackdropClick">
    <div class="bg-white rounded-lg w-full max-w-md max-h-[90vh] overflow-hidden" @click.stop>
      <!-- 头部 -->
      <div class="flex justify-between items-center p-6 border-b border-gray-200">
        <h2 class="text-xl font-bold text-gray-900">
          {{ isEdit ? '编辑用户' : '添加用户' }}
        </h2>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 表单内容 -->
      <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
        <form @submit.prevent="handleSubmit" class="space-y-4">
          <!-- 姓名 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              姓名 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入用户姓名"
            />
          </div>

          <!-- 邮箱 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              邮箱 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.email"
              type="email"
              required
              :disabled="isEdit"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              placeholder="请输入邮箱地址"
            />
            <p v-if="isEdit" class="mt-1 text-sm text-gray-500">邮箱地址不可修改</p>
          </div>

          <!-- 密码 -->
          <div v-if="!isEdit">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              密码 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.password"
              type="password"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入密码"
            />
          </div>

          <!-- 确认密码 -->
          <div v-if="!isEdit">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              确认密码 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.confirmPassword"
              type="password"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请再次输入密码"
            />
            <p v-if="form.password && form.confirmPassword && form.password !== form.confirmPassword" class="mt-1 text-sm text-red-500">
              两次输入的密码不一致
            </p>
          </div>

          <!-- 角色 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              角色 <span class="text-red-500">*</span>
            </label>
            <select
              v-model="form.role"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">请选择角色</option>
              <option value="admin">管理员</option>
              <option value="editor">编辑</option>
              <option value="viewer">查看者</option>
            </select>
            <div class="mt-2 text-sm text-gray-500">
              <p><strong>管理员</strong>：拥有所有权限，可以管理用户和审批新闻</p>
              <p><strong>编辑</strong>：可以创建和编辑新闻，提交审批</p>
              <p><strong>查看者</strong>：只能查看已发布的新闻</p>
            </div>
          </div>

          <!-- 状态 -->
          <div v-if="isEdit">
            <label class="block text-sm font-medium text-gray-700 mb-2">状态</label>
            <div class="flex items-center space-x-4">
              <label class="flex items-center">
                <input
                  v-model="form.status"
                  type="radio"
                  value="active"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span class="ml-2 text-sm text-gray-700">活跃</span>
              </label>
              <label class="flex items-center">
                <input
                  v-model="form.status"
                  type="radio"
                  value="inactive"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span class="ml-2 text-sm text-gray-700">禁用</span>
              </label>
            </div>
          </div>

          <!-- 重置密码 -->
          <div v-if="isEdit" class="border-t border-gray-200 pt-4">
            <div class="flex items-center justify-between">
              <div>
                <h4 class="text-sm font-medium text-gray-900">重置密码</h4>
                <p class="text-sm text-gray-500">为用户设置新密码</p>
              </div>
              <button
                type="button"
                @click="showPasswordReset = !showPasswordReset"
                class="text-sm text-blue-600 hover:text-blue-800"
              >
                {{ showPasswordReset ? '取消' : '重置密码' }}
              </button>
            </div>
            
            <div v-if="showPasswordReset" class="mt-4 space-y-3">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">新密码</label>
                <input
                  v-model="form.newPassword"
                  type="password"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请输入新密码"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">确认新密码</label>
                <input
                  v-model="form.confirmNewPassword"
                  type="password"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="请再次输入新密码"
                />
                <p v-if="form.newPassword && form.confirmNewPassword && form.newPassword !== form.confirmNewPassword" class="mt-1 text-sm text-red-500">
                  两次输入的密码不一致
                </p>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- 底部操作 -->
      <div class="flex justify-between items-center p-6 border-t border-gray-200 bg-gray-50">
        <div class="text-sm text-gray-500">
          <span v-if="isEdit">最后修改: {{ formatDate(form.updated_at) }}</span>
          <span v-else>创建时间: {{ formatDate(new Date().toISOString()) }}</span>
        </div>
        <div class="flex space-x-3">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            取消
          </button>
          <button
            type="button"
            @click="handleSubmit"
            :disabled="loading || !isFormValid"
            class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
          >
            {{ loading ? '保存中...' : (isEdit ? '更新' : '创建') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'

// Props
interface Props {
  user?: any
  isEdit?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isEdit: false
})

// Emits
const emit = defineEmits<{
  close: []
  save: [data: any]
}>()

// 响应式数据
const loading = ref(false)
const showPasswordReset = ref(false)

// 表单数据
const form = reactive({
  name: '',
  email: '',
  password: '',
  confirmPassword: '',
  role: '',
  status: 'active',
  newPassword: '',
  confirmNewPassword: '',
  updated_at: ''
})

// 计算属性
const isFormValid = computed(() => {
  const basicValid = form.name.trim() && form.email.trim() && form.role
  
  if (!props.isEdit) {
    // 创建用户时需要密码
    return basicValid && 
           form.password && 
           form.confirmPassword && 
           form.password === form.confirmPassword
  } else {
    // 编辑用户时，如果要重置密码，需要验证新密码
    if (showPasswordReset.value) {
      return basicValid && 
             form.newPassword && 
             form.confirmNewPassword && 
             form.newPassword === form.confirmNewPassword
    }
    return basicValid
  }
})

// 方法
const handleBackdropClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    emit('close')
  }
}

const handleSubmit = async () => {
  if (!isFormValid.value) {
    alert('请填写所有必填字段')
    return
  }
  
  loading.value = true
  try {
    const data: any = {
      name: form.name,
      email: form.email,
      role: form.role
    }
    
    if (props.isEdit) {
      data.status = form.status
      if (showPasswordReset.value && form.newPassword) {
        data.password = form.newPassword
      }
    } else {
      data.password = form.password
    }
    
    emit('save', data)
  } finally {
    loading.value = false
  }
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

// 初始化
const initializeForm = () => {
  if (props.isEdit && props.user) {
    Object.assign(form, {
      name: props.user.name || '',
      email: props.user.email || '',
      role: props.user.role || '',
      status: props.user.status || 'active',
      updated_at: props.user.updated_at || new Date().toISOString()
    })
  } else {
    // 新建时的默认值
    form.status = 'active'
    form.updated_at = new Date().toISOString()
  }
}

// 生命周期
onMounted(() => {
  initializeForm()
})
</script>

<style scoped>
/* 表单样式 */
input:focus,
select:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 单选按钮样式 */
input[type="radio"]:checked {
  background-color: #3b82f6;
  border-color: #3b82f6;
}
</style>
