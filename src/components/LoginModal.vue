<template>
  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" class="relative z-50" @close="$emit('close')">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-black bg-opacity-25" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div class="flex min-h-full items-center justify-center p-4 text-center">
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
              <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 mb-4">
                管理员登录
              </DialogTitle>

              <form @submit.prevent="handleSubmit" class="space-y-4">
                <!-- 邮箱输入 -->
                <div>
                  <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                    邮箱地址
                  </label>
                  <input
                    id="email"
                    v-model="form.email"
                    type="email"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入邮箱地址"
                  />
                </div>

                <!-- 密码输入 -->
                <div>
                  <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
                    密码
                  </label>
                  <input
                    id="password"
                    v-model="form.password"
                    type="password"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入密码"
                  />
                </div>

                <!-- 错误信息 -->
                <div v-if="authStore.error" class="text-red-600 text-sm">
                  {{ authStore.error }}
                </div>

                <!-- 默认账号提示 -->
                <div class="bg-blue-50 border border-blue-200 rounded-md p-3">
                  <h4 class="text-sm font-medium text-blue-800 mb-2">测试账号</h4>
                  <div class="text-xs text-blue-600 space-y-1">
                    <p><strong>管理员:</strong> <EMAIL> / admin123</p>
                    <p><strong>编辑:</strong> <EMAIL> / editor123</p>
                  </div>
                </div>

                <!-- 按钮组 -->
                <div class="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    @click="$emit('close')"
                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
                  >
                    取消
                  </button>
                  <button
                    type="submit"
                    :disabled="authStore.loading"
                    class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <span v-if="authStore.loading" class="flex items-center">
                      <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      登录中...
                    </span>
                    <span v-else>登录</span>
                  </button>
                </div>
              </form>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { useRouter } from 'vue-router'
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import { useAuthStore } from '@/stores/auth'

// Props & Emits
interface Props {
  isOpen: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  close: []
}>()

// Composables
const router = useRouter()
const authStore = useAuthStore()

// Reactive data
const form = ref({
  email: '',
  password: ''
})

// Methods
const handleSubmit = async () => {
  try {
    await authStore.login(form.value)
    
    // 登录成功，关闭模态框并跳转
    emit('close')
    router.push('/admin')
  } catch (error) {
    // 错误已经在 store 中处理
    console.error('登录失败:', error)
  }
}

// 监听模态框打开状态，重置表单和错误
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    form.value = {
      email: '',
      password: ''
    }
    authStore.clearError()
  }
})
</script>
