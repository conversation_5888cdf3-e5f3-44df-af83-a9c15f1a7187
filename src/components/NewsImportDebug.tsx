'use client';

import { useState, useEffect } from 'react';
import { newsApi } from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';
import { getCurrentUser } from '@/lib/mockData';
import type { NewsWithDetails } from '@/types';

export default function NewsImportDebug() {
  const [allNews, setAllNews] = useState<NewsWithDetails[]>([]);
  const [publishedNews, setPublishedNews] = useState<NewsWithDetails[]>([]);
  const { user, isAdmin } = useAuth();
  const mockCurrentUser = getCurrentUser();

  useEffect(() => {
    loadAllNews();
    loadPublishedNews();
  }, []);

  const loadAllNews = async () => {
    try {
      // 获取所有新闻（不筛选状态）
      const data = await newsApi.getNews({ limit: 50 });
      console.log('调试页面加载所有新闻:', data.length, '条');
      setAllNews(data);
    } catch (error) {
      console.error('加载所有新闻失败:', error);
    }
  };

  const loadPublishedNews = async () => {
    try {
      // 只获取已发布的新闻
      const data = await newsApi.getNews({ status: 'published', limit: 50 });
      console.log('调试页面加载已发布新闻:', data.length, '条');
      setPublishedNews(data);
    } catch (error) {
      console.error('加载已发布新闻失败:', error);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { label: '草稿', className: 'bg-gray-100 text-gray-800' },
      pending: { label: '待审批', className: 'bg-yellow-100 text-yellow-800' },
      published: { label: '已发布', className: 'bg-green-100 text-green-800' },
      rejected: { label: '已拒绝', className: 'bg-red-100 text-red-800' },
      archived: { label: '已归档', className: 'bg-purple-100 text-purple-800' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.className}`}>
        {config.label}
      </span>
    );
  };

  return (
    <div className="max-w-6xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">新闻导入调试信息</h2>
      
      {/* 用户信息 */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="text-lg font-medium text-blue-900 mb-2">当前用户信息</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="text-sm text-blue-800">
            <h4 className="font-medium mb-2">Auth Context 用户:</h4>
            <p><strong>用户:</strong> {user?.name || '未登录'}</p>
            <p><strong>邮箱:</strong> {user?.email || '无'}</p>
            <p><strong>角色:</strong> {user?.role || '无'}</p>
            <p><strong>是否管理员:</strong> {isAdmin() ? '是' : '否'}</p>
          </div>
          <div className="text-sm text-blue-800">
            <h4 className="font-medium mb-2">Mock Data 用户:</h4>
            <p><strong>用户:</strong> {mockCurrentUser?.name || '未登录'}</p>
            <p><strong>邮箱:</strong> {mockCurrentUser?.email || '无'}</p>
            <p><strong>角色:</strong> {mockCurrentUser?.role || '无'}</p>
            <p><strong>ID:</strong> {mockCurrentUser?.id || '无'}</p>
          </div>
        </div>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <div className="p-4 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-medium text-gray-900 mb-2">所有新闻统计</h3>
          <p className="text-2xl font-bold text-gray-700">{allNews.length} 条</p>
          <div className="mt-2 text-sm text-gray-600">
            {Object.entries(allNews.reduce((acc, news) => {
              acc[news.status] = (acc[news.status] || 0) + 1;
              return acc;
            }, {} as Record<string, number>)).map(([status, count]) => (
              <div key={status} className="flex justify-between">
                <span>{status}:</span>
                <span>{count}</span>
              </div>
            ))}
          </div>
        </div>
        
        <div className="p-4 bg-green-50 rounded-lg">
          <h3 className="text-lg font-medium text-green-900 mb-2">已发布新闻</h3>
          <p className="text-2xl font-bold text-green-700">{publishedNews.length} 条</p>
          <p className="text-sm text-green-600">首页显示的新闻数量</p>
        </div>
      </div>

      {/* 所有新闻列表 */}
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">所有新闻列表</h3>
        <div className="space-y-2">
          {allNews.length === 0 ? (
            <p className="text-gray-500">暂无新闻</p>
          ) : (
            allNews.map((news) => (
              <div key={news.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{news.title}</h4>
                  <p className="text-sm text-gray-600">作者: {news.author.name} | 分类: {news.category.name}</p>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusBadge(news.status)}
                  <span className="text-xs text-gray-500">
                    {new Date(news.created_at).toLocaleString()}
                  </span>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* 刷新按钮 */}
      <div className="flex justify-center">
        <button
          onClick={() => {
            loadAllNews();
            loadPublishedNews();
          }}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          刷新数据
        </button>
      </div>
    </div>
  );
}
