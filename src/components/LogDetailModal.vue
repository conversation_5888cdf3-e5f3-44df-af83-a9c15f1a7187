<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="handleBackdropClick">
    <div class="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-hidden" @click.stop>
      <!-- 头部 -->
      <div class="flex justify-between items-center p-6 border-b border-gray-200">
        <h2 class="text-xl font-bold text-gray-900">日志详情</h2>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 内容 -->
      <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
        <div class="space-y-6">
          <!-- 基本信息 -->
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">基本信息</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">日志ID</label>
                <p class="mt-1 text-sm text-gray-900 font-mono">{{ log.id }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">时间</label>
                <p class="mt-1 text-sm text-gray-900">{{ formatDateTime(log.created_at) }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">级别</label>
                <span 
                  class="mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getLevelClass(log.level)"
                >
                  {{ getLevelText(log.level) }}
                </span>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">操作类型</label>
                <span 
                  class="mt-1 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getActionClass(log.action)"
                >
                  {{ getActionText(log.action) }}
                </span>
              </div>
            </div>
          </div>

          <!-- 用户信息 -->
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">用户信息</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">用户名</label>
                <div class="mt-1 flex items-center">
                  <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-semibold mr-3">
                    {{ log.user_name.charAt(0).toUpperCase() }}
                  </div>
                  <div>
                    <p class="text-sm font-medium text-gray-900">{{ log.user_name }}</p>
                    <p class="text-xs text-gray-500">{{ log.user_role }}</p>
                  </div>
                </div>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">IP地址</label>
                <p class="mt-1 text-sm text-gray-900 font-mono">{{ log.ip_address }}</p>
              </div>
            </div>
          </div>

          <!-- 操作描述 -->
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">操作描述</h3>
            <div class="bg-gray-50 rounded-lg p-4">
              <p class="text-sm text-gray-900">{{ log.description }}</p>
            </div>
          </div>

          <!-- 详细信息 -->
          <div v-if="log.details">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">详细信息</h3>
            <div class="bg-gray-50 rounded-lg p-4">
              <pre class="text-sm text-gray-900 whitespace-pre-wrap">{{ formatDetails(log.details) }}</pre>
            </div>
          </div>

          <!-- 技术信息 -->
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">技术信息</h3>
            <div class="space-y-3">
              <div>
                <label class="block text-sm font-medium text-gray-700">User Agent</label>
                <p class="mt-1 text-xs text-gray-600 font-mono break-all">{{ log.user_agent }}</p>
              </div>
              <div v-if="log.details?.session_id">
                <label class="block text-sm font-medium text-gray-700">会话ID</label>
                <p class="mt-1 text-xs text-gray-600 font-mono">{{ log.details.session_id }}</p>
              </div>
            </div>
          </div>

          <!-- 安全警告 -->
          <div v-if="log.details?.security_alert" class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">安全警告</h3>
                <p class="mt-1 text-sm text-red-700">此操作触发了安全警报，请注意检查。</p>
              </div>
            </div>
          </div>

          <!-- 相关操作 -->
          <div v-if="log.level === 'error' || log.level === 'critical'">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">建议操作</h3>
            <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
              <div class="flex">
                <svg class="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-yellow-800">处理建议</h3>
                  <div class="mt-1 text-sm text-yellow-700">
                    <ul class="list-disc list-inside space-y-1">
                      <li v-if="log.level === 'critical'">立即检查系统安全状态</li>
                      <li v-if="log.action === 'login' && log.level === 'error'">检查是否存在暴力破解攻击</li>
                      <li v-if="log.details?.error_code">查看错误代码文档：{{ log.details.error_code }}</li>
                      <li>联系技术支持团队进行进一步分析</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作 -->
      <div class="flex justify-between items-center p-6 border-t border-gray-200 bg-gray-50">
        <div class="text-sm text-gray-500">
          记录时间: {{ formatDateTime(log.created_at) }}
        </div>
        <div class="flex space-x-3">
          <button
            @click="copyLogInfo"
            class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            复制信息
          </button>
          <button
            @click="$emit('close')"
            class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

// Props
interface Props {
  log: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

// 方法
const handleBackdropClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    emit('close')
  }
}

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const formatDetails = (details: any) => {
  return JSON.stringify(details, null, 2)
}

const getLevelClass = (level: string) => {
  switch (level) {
    case 'info':
      return 'bg-blue-100 text-blue-800'
    case 'warning':
      return 'bg-yellow-100 text-yellow-800'
    case 'error':
      return 'bg-red-100 text-red-800'
    case 'critical':
      return 'bg-red-100 text-red-800 font-bold'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getLevelText = (level: string) => {
  switch (level) {
    case 'info':
      return '信息'
    case 'warning':
      return '警告'
    case 'error':
      return '错误'
    case 'critical':
      return '严重'
    default:
      return '未知'
  }
}

const getActionClass = (action: string) => {
  switch (action) {
    case 'login':
      return 'bg-green-100 text-green-800'
    case 'logout':
      return 'bg-gray-100 text-gray-800'
    case 'create':
      return 'bg-blue-100 text-blue-800'
    case 'update':
      return 'bg-yellow-100 text-yellow-800'
    case 'delete':
      return 'bg-red-100 text-red-800'
    case 'approve':
      return 'bg-green-100 text-green-800'
    case 'reject':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getActionText = (action: string) => {
  switch (action) {
    case 'login':
      return '登录'
    case 'logout':
      return '登出'
    case 'create':
      return '创建'
    case 'update':
      return '更新'
    case 'delete':
      return '删除'
    case 'approve':
      return '审批'
    case 'reject':
      return '拒绝'
    default:
      return '未知'
  }
}

const copyLogInfo = () => {
  const logInfo = `
日志ID: ${props.log.id}
时间: ${formatDateTime(props.log.created_at)}
级别: ${getLevelText(props.log.level)}
用户: ${props.log.user_name} (${props.log.user_role})
操作: ${getActionText(props.log.action)}
描述: ${props.log.description}
IP地址: ${props.log.ip_address}
详细信息: ${props.log.details ? JSON.stringify(props.log.details, null, 2) : '无'}
  `.trim()
  
  navigator.clipboard.writeText(logInfo).then(() => {
    alert('日志信息已复制到剪贴板')
  }).catch(() => {
    alert('复制失败，请手动选择文本复制')
  })
}
</script>

<style scoped>
/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 代码块样式 */
pre {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
}
</style>
