<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="handleBackdropClick">
    <div class="bg-white rounded-lg w-full max-w-md max-h-[90vh] overflow-hidden" @click.stop>
      <!-- 头部 -->
      <div class="flex justify-between items-center p-6 border-b border-gray-200">
        <h2 class="text-xl font-bold text-gray-900">
          {{ isEdit ? '编辑标签' : '添加标签' }}
        </h2>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 表单内容 -->
      <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
        <form @submit.prevent="handleSubmit" class="space-y-4">
          <!-- 标签名称 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              标签名称 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入标签名称"
            />
            <p class="mt-1 text-sm text-gray-500">建议使用简短的词汇，如"重要"、"热点"等</p>
          </div>

          <!-- 标签描述 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              标签描述
            </label>
            <textarea
              v-model="form.description"
              rows="2"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入标签描述"
            ></textarea>
          </div>

          <!-- 标签颜色 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              标签颜色 <span class="text-red-500">*</span>
            </label>
            <div class="flex items-center space-x-3">
              <input
                v-model="form.color"
                type="color"
                class="w-12 h-10 border border-gray-300 rounded cursor-pointer"
              />
              <input
                v-model="form.color"
                type="text"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="#3b82f6"
              />
            </div>
            <p class="mt-1 text-sm text-gray-500">选择标签的主题颜色</p>
          </div>

          <!-- 预设颜色 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">快速选择</label>
            <div class="grid grid-cols-8 gap-2">
              <button
                v-for="color in presetColors"
                :key="color"
                type="button"
                @click="form.color = color"
                class="w-8 h-8 rounded-full border-2 hover:scale-110 transition-transform"
                :class="form.color === color ? 'border-gray-400' : 'border-gray-200'"
                :style="{ backgroundColor: color }"
                :title="color"
              ></button>
            </div>
          </div>

          <!-- 标签类型 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              标签类型
            </label>
            <select
              v-model="form.type"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="general">通用标签</option>
              <option value="priority">优先级标签</option>
              <option value="category">分类标签</option>
              <option value="status">状态标签</option>
            </select>
            <p class="mt-1 text-sm text-gray-500">选择标签的用途类型</p>
          </div>

          <!-- 排序权重 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              排序权重
            </label>
            <input
              v-model.number="form.sort_order"
              type="number"
              min="0"
              max="999"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="0"
            />
            <p class="mt-1 text-sm text-gray-500">数值越大排序越靠前，默认为0</p>
          </div>

          <!-- 是否推荐 -->
          <div>
            <label class="flex items-center">
              <input
                v-model="form.is_recommended"
                type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span class="ml-2 text-sm text-gray-700">推荐标签</span>
            </label>
            <p class="mt-1 text-sm text-gray-500">推荐标签会在创建新闻时优先显示</p>
          </div>

          <!-- 预览 -->
          <div class="border-t border-gray-200 pt-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">预览</label>
            <div class="p-3 border border-gray-200 rounded-lg bg-gray-50">
              <div class="flex items-center space-x-3">
                <span 
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :style="{ backgroundColor: form.color + '20', color: form.color }"
                >
                  {{ form.name || '标签名称' }}
                </span>
                <div>
                  <p class="text-sm text-gray-500">{{ form.description || '标签描述' }}</p>
                  <div class="flex items-center space-x-2 text-xs text-gray-400 mt-1">
                    <span>{{ getTypeText(form.type) }}</span>
                    <span v-if="form.is_recommended" class="text-blue-500">• 推荐</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 使用统计 -->
          <div v-if="isEdit && tag?.usage_count !== undefined" class="border-t border-gray-200 pt-4">
            <label class="block text-sm font-medium text-gray-700 mb-2">使用统计</label>
            <div class="bg-gray-50 rounded-lg p-3">
              <div class="flex justify-between items-center">
                <span class="text-sm text-gray-600">使用次数</span>
                <span class="text-lg font-semibold text-gray-900">{{ tag.usage_count || 0 }}</span>
              </div>
              <div class="flex justify-between items-center mt-2">
                <span class="text-sm text-gray-600">创建时间</span>
                <span class="text-sm text-gray-500">{{ formatDate(tag.created_at) }}</span>
              </div>
            </div>
          </div>
        </form>
      </div>

      <!-- 底部操作 -->
      <div class="flex justify-between items-center p-6 border-t border-gray-200 bg-gray-50">
        <div class="text-sm text-gray-500">
          <span v-if="isEdit">最后修改: {{ formatDate(form.updated_at) }}</span>
          <span v-else>创建时间: {{ formatDate(new Date().toISOString()) }}</span>
        </div>
        <div class="flex space-x-3">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            取消
          </button>
          <button
            type="button"
            @click="handleSubmit"
            :disabled="loading || !isFormValid"
            class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
          >
            {{ loading ? '保存中...' : (isEdit ? '更新' : '创建') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'

// Props
interface Props {
  tag?: any
  isEdit?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isEdit: false
})

// Emits
const emit = defineEmits<{
  close: []
  save: [data: any]
}>()

// 响应式数据
const loading = ref(false)

// 预设颜色
const presetColors = [
  '#ef4444', '#f59e0b', '#10b981', '#3b82f6',
  '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16',
  '#f97316', '#6366f1', '#14b8a6', '#eab308',
  '#f43f5e', '#a855f7', '#0ea5e9', '#22c55e'
]

// 表单数据
const form = reactive({
  name: '',
  description: '',
  color: '#3b82f6',
  type: 'general',
  sort_order: 0,
  is_recommended: false,
  updated_at: ''
})

// 计算属性
const isFormValid = computed(() => {
  return form.name.trim() && form.color
})

// 方法
const handleBackdropClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    emit('close')
  }
}

const handleSubmit = async () => {
  if (!isFormValid.value) {
    alert('请填写所有必填字段')
    return
  }
  
  loading.value = true
  try {
    const data = {
      name: form.name,
      description: form.description,
      color: form.color,
      type: form.type,
      sort_order: form.sort_order || 0,
      is_recommended: form.is_recommended
    }
    
    emit('save', data)
  } finally {
    loading.value = false
  }
}

const formatDate = (dateString: string) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('zh-CN')
}

const getTypeText = (type: string) => {
  switch (type) {
    case 'general':
      return '通用标签'
    case 'priority':
      return '优先级标签'
    case 'category':
      return '分类标签'
    case 'status':
      return '状态标签'
    default:
      return '通用标签'
  }
}

// 初始化
const initializeForm = () => {
  if (props.isEdit && props.tag) {
    Object.assign(form, {
      name: props.tag.name || '',
      description: props.tag.description || '',
      color: props.tag.color || '#3b82f6',
      type: props.tag.type || 'general',
      sort_order: props.tag.sort_order || 0,
      is_recommended: props.tag.is_recommended || false,
      updated_at: props.tag.updated_at || new Date().toISOString()
    })
  } else {
    // 新建时的默认值
    form.color = '#3b82f6'
    form.type = 'general'
    form.sort_order = 0
    form.is_recommended = false
    form.updated_at = new Date().toISOString()
  }
}

// 生命周期
onMounted(() => {
  initializeForm()
})
</script>

<style scoped>
/* 表单样式 */
input:focus,
textarea:focus,
select:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 复选框样式 */
input[type="checkbox"]:checked {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

/* 颜色选择器样式 */
input[type="color"] {
  -webkit-appearance: none;
  border: none;
  cursor: pointer;
}

input[type="color"]::-webkit-color-swatch-wrapper {
  padding: 0;
}

input[type="color"]::-webkit-color-swatch {
  border: none;
  border-radius: 4px;
}
</style>
