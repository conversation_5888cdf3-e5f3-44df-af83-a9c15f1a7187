'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import {
  EyeIcon,
  CalendarIcon,
  UserIcon,
  FolderIcon,
  TagIcon,
  MagnifyingGlassIcon,
  ArrowRightOnRectangleIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { newsApi, categoryApi } from '@/lib/api';
import { newsEvents } from '@/lib/eventBus';
import NewsViewer from './NewsViewer';
import type { NewsWithDetails, Category, NewsFilters } from '@/types';

interface PublicNewsDisplayProps {
  onLogin: () => void;
}

export default function PublicNewsDisplay({ onLogin }: PublicNewsDisplayProps) {
  const [news, setNews] = useState<NewsWithDetails[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedNews, setSelectedNews] = useState<NewsWithDetails | null>(null);
  const [showViewer, setShowViewer] = useState(false);
  const [lastRefresh, setLastRefresh] = useState(Date.now());
  const [refreshing, setRefreshing] = useState(false);
  const [refreshMessage, setRefreshMessage] = useState<string>('');
  const [filters, setFilters] = useState<NewsFilters>({
    status: 'published', // 只显示已审批通过的新闻
    page: 1,
    limit: 50, // 增加显示数量，确保显示所有已发布的新闻
  });

  useEffect(() => {
    loadData();
  }, [filters, lastRefresh]);

  useEffect(() => {
    loadCategories();
  }, []);

  // 定期刷新数据（每30秒）
  useEffect(() => {
    const interval = setInterval(() => {
      setLastRefresh(Date.now());
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  // 监听新闻数据变更事件
  useEffect(() => {
    const handleRefreshNeeded = () => {
      console.log('收到新闻数据变更事件，刷新首页数据...');
      setLastRefresh(Date.now());
    };

    // 订阅事件
    newsEvents.onRefreshNeeded(handleRefreshNeeded);

    // 清理函数
    return () => {
      newsEvents.offRefreshNeeded(handleRefreshNeeded);
    };
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const data = await newsApi.getNews(filters);
      console.log('公开页面加载已审批通过的新闻:', data.length, '条，筛选条件:', filters);
      setNews(data);

      // 如果是手动刷新，显示成功消息
      if (refreshing) {
        setRefreshMessage(`✅ 刷新成功！加载了 ${data.length} 条新闻`);
        setTimeout(() => setRefreshMessage(''), 3000);
      }
    } catch (error) {
      console.error('加载新闻失败:', error);
      if (refreshing) {
        setRefreshMessage('❌ 刷新失败，请重试');
        setTimeout(() => setRefreshMessage(''), 3000);
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadCategories = async () => {
    try {
      const data = await categoryApi.getCategories();
      setCategories(data);
    } catch (error) {
      console.error('加载分类失败:', error);
    }
  };

  const handleFilterChange = (key: keyof NewsFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined,
      page: 1, // 重置页码
    }));
  };

  const handleViewNews = (newsItem: NewsWithDetails) => {
    setSelectedNews(newsItem);
    setShowViewer(true);
  };

  const handleCloseViewer = () => {
    setShowViewer(false);
    setSelectedNews(null);
    // 刷新数据以更新浏览量
    loadData();
  };

  const handleManualRefresh = async () => {
    console.log('手动刷新新闻数据...');
    setRefreshing(true);
    setLastRefresh(Date.now());
    // 直接调用 loadData 确保立即刷新
    await loadData();
  };

  if (showViewer && selectedNews) {
    return (
      <NewsViewer
        news={selectedNews}
        onClose={handleCloseViewer}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部导航 */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">
                荣联科技新闻中心
              </h1>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleManualRefresh}
                disabled={refreshing}
                className={`inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed ${
                  refreshing ? 'animate-pulse' : ''
                }`}
                title={refreshing ? "刷新中..." : "刷新新闻"}
              >
                <ArrowPathIcon className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              </button>
              <button
                onClick={onLogin}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <ArrowRightOnRectangleIcon className="h-4 w-4 mr-2" />
                管理员登录
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 刷新消息 */}
        {refreshMessage && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-lg">
            <p className="text-sm text-green-800">{refreshMessage}</p>
          </div>
        )}

        {/* 搜索和筛选 */}
        <div className="mb-8 bg-white rounded-lg shadow-sm p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索新闻标题或内容..."
                  value={filters.search || ''}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={filters.category_id || ''}
                onChange={(e) => handleFilterChange('category_id', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">所有分类</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* 新闻列表 */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : news.length === 0 ? (
          <div className="text-center py-12 bg-white rounded-lg shadow-sm">
            <div className="text-gray-500">
              <h3 className="text-lg font-medium mb-2">暂无新闻</h3>
              <p>目前没有已审批通过的新闻内容</p>
              <p className="text-sm mt-2">管理员可以登录后台导入或创建新闻</p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {news.map((item) => (
              <article
                key={item.id}
                className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden cursor-pointer"
                onClick={() => handleViewNews(item)}
              >
                <div className="p-6">
                  {/* 标题 */}
                  <h2 className="text-lg font-semibold text-gray-900 mb-3 line-clamp-2 hover:text-blue-600 transition-colors">
                    {item.title}
                  </h2>

                  {/* 摘要 */}
                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {item.excerpt}
                  </p>

                  {/* 元信息 */}
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-xs text-gray-500">
                      <FolderIcon className="h-4 w-4 mr-1" />
                      <span>{item.category.name}</span>
                    </div>
                    <div className="flex items-center text-xs text-gray-500">
                      <UserIcon className="h-4 w-4 mr-1" />
                      <span>{item.author.name}</span>
                    </div>
                    <div className="flex items-center text-xs text-gray-500">
                      <CalendarIcon className="h-4 w-4 mr-1" />
                      <span>
                        {format(new Date(item.published_at || item.created_at), 'yyyy年MM月dd日', { locale: zhCN })}
                      </span>
                    </div>
                  </div>

                  {/* 标签 */}
                  {item.tags && item.tags.length > 0 && (
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-1">
                        {item.tags.slice(0, 3).map((tag, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {tag}
                          </span>
                        ))}
                        {item.tags.length > 3 && (
                          <span className="text-xs text-gray-500">
                            +{item.tags.length - 3}
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* 底部信息 */}
                  <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                    <div className="flex items-center text-xs text-gray-500">
                      <EyeIcon className="h-4 w-4 mr-1" />
                      <span>{item.view_count} 次浏览</span>
                    </div>
                    <span className="text-xs text-blue-600 font-medium">
                      阅读全文 →
                    </span>
                  </div>
                </div>
              </article>
            ))}
          </div>
        )}

        {/* 加载更多按钮 */}
        {news.length > 0 && news.length >= (filters.limit || 50) && (
          <div className="text-center mt-8">
            <button
              onClick={() => setFilters(prev => ({ ...prev, limit: (prev.limit || 50) + 20 }))}
              className="inline-flex items-center px-6 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              加载更多新闻
            </button>
          </div>
        )}

        {/* 显示总数信息 */}
        {news.length > 0 && (
          <div className="text-center mt-4">
            <p className="text-sm text-gray-500">
              已显示 {news.length} 条已审批通过的新闻
            </p>
          </div>
        )}
      </main>

      {/* 页脚 */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-500 text-sm">
            <p>&copy; 2024 荣联科技新闻中心. 保留所有权利.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
