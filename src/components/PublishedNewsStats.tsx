'use client';

import { useState, useEffect } from 'react';
import { newsApi } from '@/lib/api';
import { newsEvents } from '@/lib/eventBus';
import { 
  NewspaperIcon, 
  CheckCircleIcon, 
  ClockIcon,
  EyeIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import type { NewsWithDetails } from '@/types';

export default function PublishedNewsStats() {
  const [publishedNews, setPublishedNews] = useState<NewsWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<string>('');

  useEffect(() => {
    loadPublishedNews();
  }, []);

  // 监听新闻变更事件
  useEffect(() => {
    const handleRefreshNeeded = () => {
      console.log('统计组件收到刷新事件，重新加载数据...');
      loadPublishedNews();
    };

    newsEvents.onRefreshNeeded(handleRefreshNeeded);

    return () => {
      newsEvents.offRefreshNeeded(handleRefreshNeeded);
    };
  }, []);

  const loadPublishedNews = async () => {
    try {
      setLoading(true);
      const data = await newsApi.getNews({ 
        status: 'published', 
        limit: 100 // 获取所有已发布的新闻
      });
      setPublishedNews(data);
      setLastUpdate(new Date().toLocaleString());
      console.log('已发布新闻统计:', data.length, '条');
    } catch (error) {
      console.error('加载已发布新闻统计失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const totalViews = publishedNews.reduce((sum, news) => sum + news.view_count, 0);
  const recentNews = publishedNews.filter(news => {
    const publishedDate = new Date(news.published_at || news.created_at);
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    return publishedDate >= sevenDaysAgo;
  });

  const categoryStats = publishedNews.reduce((acc, news) => {
    const categoryName = news.category.name;
    acc[categoryName] = (acc[categoryName] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return (
    <div className="max-w-6xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900">已发布新闻统计</h2>
        <button
          onClick={loadPublishedNews}
          disabled={loading}
          className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
        >
          <ArrowPathIcon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          刷新统计
        </button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center h-32">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      ) : (
        <>
          {/* 统计卡片 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-blue-50 p-6 rounded-lg">
              <div className="flex items-center">
                <NewspaperIcon className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-blue-600">总新闻数</p>
                  <p className="text-2xl font-bold text-blue-900">{publishedNews.length}</p>
                </div>
              </div>
            </div>

            <div className="bg-green-50 p-6 rounded-lg">
              <div className="flex items-center">
                <CheckCircleIcon className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-green-600">最近7天</p>
                  <p className="text-2xl font-bold text-green-900">{recentNews.length}</p>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 p-6 rounded-lg">
              <div className="flex items-center">
                <EyeIcon className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-purple-600">总浏览量</p>
                  <p className="text-2xl font-bold text-purple-900">{totalViews.toLocaleString()}</p>
                </div>
              </div>
            </div>

            <div className="bg-orange-50 p-6 rounded-lg">
              <div className="flex items-center">
                <ClockIcon className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <p className="text-sm font-medium text-orange-600">最后更新</p>
                  <p className="text-sm font-bold text-orange-900">{lastUpdate}</p>
                </div>
              </div>
            </div>
          </div>

          {/* 分类统计 */}
          <div className="mb-8">
            <h3 className="text-lg font-medium text-gray-900 mb-4">分类统计</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {Object.entries(categoryStats).map(([category, count]) => (
                <div key={category} className="bg-gray-50 p-4 rounded-lg">
                  <p className="text-sm font-medium text-gray-600">{category}</p>
                  <p className="text-xl font-bold text-gray-900">{count} 条</p>
                </div>
              ))}
            </div>
          </div>

          {/* 最新发布的新闻 */}
          <div>
            <h3 className="text-lg font-medium text-gray-900 mb-4">最新发布的新闻</h3>
            <div className="space-y-3">
              {publishedNews.slice(0, 5).map((news) => (
                <div key={news.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{news.title}</h4>
                    <p className="text-sm text-gray-600">
                      {news.category.name} • {news.author.name} • 
                      {new Date(news.published_at || news.created_at).toLocaleDateString()}
                    </p>
                  </div>
                  <div className="flex items-center text-sm text-gray-500">
                    <EyeIcon className="h-4 w-4 mr-1" />
                    {news.view_count}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 说明信息 */}
          <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="text-lg font-medium text-blue-900 mb-2">说明</h4>
            <div className="text-sm text-blue-800 space-y-1">
              <p>• <strong>已发布新闻</strong>: 显示所有状态为 'published' 的新闻</p>
              <p>• <strong>审批流程</strong>: 编辑创建的新闻需要管理员审批后才能发布</p>
              <p>• <strong>管理员权限</strong>: 管理员创建的新闻可以直接发布</p>
              <p>• <strong>首页显示</strong>: 只有已发布的新闻才会在首页显示</p>
              <p>• <strong>实时更新</strong>: 统计数据会随着新闻的创建、审批自动更新</p>
            </div>
          </div>
        </>
      )}
    </div>
  );
}
