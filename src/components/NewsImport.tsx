'use client';

import { useState } from 'react';
import { CloudArrowUpIcon, DocumentTextIcon, CheckCircleIcon } from '@heroicons/react/24/outline';
import { newsApi } from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';
import type { CreateNewsData } from '@/types';

// 从搜索结果整理的荣联科技新闻数据
const ronglianNewsData = [
  {
    title: '荣联科技集团CEO获得"2024福布斯中国科创人物"奖项',
    content: `<h2>获奖详情</h2>
    <p>4月18日，环球科创大会暨2024福布斯中国科创人物评选颁奖盛典于合肥盛大举办。荣联科技集团总裁兼CEO张旭光应邀出席，凭借其在数字化服务领域的卓越贡献，荣获"2024福布斯中国科创人物"奖项。</p>
    
    <h3>获奖意义</h3>
    <ul>
      <li>认可荣联科技在数字化转型领域的领导地位</li>
      <li>彰显公司在科技创新方面的突出成就</li>
      <li>提升荣联科技在行业内的影响力和知名度</li>
    </ul>
    
    <p>此次获奖是对荣联科技集团在数字化服务领域持续创新和卓越表现的重要认可，也进一步巩固了公司在行业内的领先地位。</p>`,
    excerpt: '荣联科技集团总裁兼CEO张旭光在环球科创大会上荣获"2024福布斯中国科创人物"奖项，彰显公司在数字化服务领域的卓越贡献。',
    category_id: '1', // 公司新闻
    tags: ['获奖', 'CEO', '福布斯', '科创人物'],
    source_url: 'https://news.qq.com/rain/a/20250423A02YO100'
  },
  {
    title: '荣联科技集团参与起草的两项云计算领域国家标准正式发布',
    content: `<h2>标准发布</h2>
    <p>近日，国家市场监督管理总局、国家标准化管理委员会发布中华人民共和国国家标准公告（2024年第17号）。荣联科技集团参与起草的国家标准GB/T正式发布。</p>
    
    <h3>标准意义</h3>
    <ul>
      <li>推动云计算行业标准化发展</li>
      <li>提升行业技术规范和服务质量</li>
      <li>为云计算服务提供统一的技术标准</li>
      <li>促进云计算产业健康发展</li>
    </ul>
    
    <h3>荣联科技的贡献</h3>
    <p>作为参与起草单位，荣联科技集团凭借在云计算领域的深厚技术积累和丰富实践经验，为标准的制定提供了重要的技术支撑和实践案例。</p>
    
    <p>这两项国家标准的发布，将进一步规范云计算服务市场，提升服务质量，推动整个行业的健康发展。</p>`,
    excerpt: '荣联科技集团参与起草的两项云计算领域国家标准正式发布，为云计算行业标准化发展做出重要贡献。',
    category_id: '3', // 产品发布
    tags: ['国家标准', '云计算', '技术标准', '行业规范'],
    source_url: 'https://finance.sina.com.cn/roll/2024-09-03/doc-incmwrzh0166621.shtml'
  },
  {
    title: '荣联科技集团中标中科院软件研究所基础设施平台服务器采购项目',
    content: `<h2>中标详情</h2>
    <p>近日，荣联科技集团成功中标中科院软件研究所基础设施平台服务器采购项目，这是公司在科研院所市场的又一重要突破。</p>
    
    <h3>项目意义</h3>
    <ul>
      <li>进一步拓展科研院所市场</li>
      <li>展示公司在基础设施建设方面的专业能力</li>
      <li>加强与中科院系统的合作关系</li>
      <li>提升公司在高端客户群体中的影响力</li>
    </ul>
    
    <h3>技术优势</h3>
    <p>荣联科技集团凭借在IT基础设施建设方面的丰富经验和技术优势，为中科院软件研究所提供高性能、高可靠性的服务器解决方案。</p>
    
    <p>此次中标不仅体现了客户对荣联科技技术实力和服务能力的认可，也为公司在科研领域的进一步发展奠定了坚实基础。</p>`,
    excerpt: '荣联科技集团成功中标中科院软件研究所基础设施平台服务器采购项目，展示公司在IT基础设施建设方面的专业实力。',
    category_id: '1', // 公司新闻
    tags: ['中标', '中科院', '服务器', '基础设施'],
    source_url: 'https://www.nbd.com.cn/articles/2024-12-03/3669965.html'
  },
  {
    title: '荣联科技：2024年净利润2810.69万元同比扭亏',
    content: `<h2>财务业绩</h2>
    <p>荣联科技发布2024年年度报告，公司实现净利润2810.69万元，相比上年同期实现扭亏为盈，经营状况显著改善。</p>
    
    <h3>主要财务指标</h3>
    <ul>
      <li>净利润：2810.69万元，同比扭亏</li>
      <li>营业收入稳步增长</li>
      <li>经营活动现金流净额大幅改善</li>
      <li>资产负债结构持续优化</li>
    </ul>
    
    <h3>业绩改善原因</h3>
    <p>公司业绩改善主要得益于：</p>
    <ul>
      <li>数字化转型业务快速发展</li>
      <li>云计算服务收入增长</li>
      <li>成本控制措施有效实施</li>
      <li>运营效率持续提升</li>
    </ul>
    
    <p>这一业绩表现充分体现了公司战略转型的成效，为未来持续发展奠定了良好基础。</p>`,
    excerpt: '荣联科技2024年实现净利润2810.69万元，同比扭亏为盈，经营状况显著改善，战略转型成效显著。',
    category_id: '1', // 公司新闻
    tags: ['财报', '净利润', '扭亏', '业绩'],
    source_url: 'https://www.stcn.com/article/detail/1635178.html'
  }
];

interface NewsImportProps {
  onImportComplete: () => void;
}

export default function NewsImport({ onImportComplete }: NewsImportProps) {
  const [importing, setImporting] = useState(false);
  const [importedCount, setImportedCount] = useState(0);
  const [selectedNews, setSelectedNews] = useState<number[]>([]);
  const { isAdmin } = useAuth();

  const handleSelectAll = () => {
    if (selectedNews.length === ronglianNewsData.length) {
      setSelectedNews([]);
    } else {
      setSelectedNews(ronglianNewsData.map((_, index) => index));
    }
  };

  const handleSelectNews = (index: number) => {
    setSelectedNews(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    );
  };

  const handleImport = async () => {
    if (selectedNews.length === 0) {
      alert('请选择要导入的新闻');
      return;
    }

    setImporting(true);
    setImportedCount(0);

    try {
      for (let i = 0; i < selectedNews.length; i++) {
        const newsIndex = selectedNews[i];
        const newsItem = ronglianNewsData[newsIndex];
        
        const newsData: CreateNewsData = {
          title: newsItem.title,
          content: newsItem.content,
          excerpt: newsItem.excerpt,
          category_id: newsItem.category_id,
          status: isAdmin() ? 'published' : 'pending', // 管理员直接发布，编辑需要审批
          tags: newsItem.tags,
        };

        await newsApi.createNews(newsData);
        setImportedCount(i + 1);
        
        // 添加延迟避免请求过快
        await new Promise(resolve => setTimeout(resolve, 500));
      }

      alert(`成功导入 ${selectedNews.length} 条新闻！`);
      onImportComplete();
    } catch (error) {
      console.error('导入新闻失败:', error);
      alert('导入失败，请重试');
    } finally {
      setImporting(false);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">
          荣联科技官网新闻导入
        </h2>
        <p className="text-gray-600">
          从荣联科技官网和权威媒体搜集的最新新闻，选择需要导入的新闻到系统中。
        </p>
      </div>

      {/* 操作栏 */}
      <div className="flex justify-between items-center mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-4">
          <button
            onClick={handleSelectAll}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            {selectedNews.length === ronglianNewsData.length ? '取消全选' : '全选'}
          </button>
          <span className="text-sm text-gray-600">
            已选择 {selectedNews.length} / {ronglianNewsData.length} 条新闻
          </span>
        </div>
        <button
          onClick={handleImport}
          disabled={importing || selectedNews.length === 0}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <CloudArrowUpIcon className="h-4 w-4 mr-2" />
          {importing ? `导入中... (${importedCount}/${selectedNews.length})` : '导入选中新闻'}
        </button>
      </div>

      {/* 新闻列表 */}
      <div className="space-y-4">
        {ronglianNewsData.map((news, index) => (
          <div
            key={index}
            className={`border rounded-lg p-4 cursor-pointer transition-colors ${
              selectedNews.includes(index)
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => handleSelectNews(index)}
          >
            <div className="flex items-start space-x-3">
              <input
                type="checkbox"
                checked={selectedNews.includes(index)}
                onChange={() => handleSelectNews(index)}
                className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <div className="flex-1">
                <div className="flex items-start justify-between">
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    {news.title}
                  </h3>
                  <DocumentTextIcon className="h-5 w-5 text-gray-400 ml-2" />
                </div>
                <p className="text-gray-600 text-sm mb-3">
                  {news.excerpt}
                </p>
                <div className="flex items-center justify-between">
                  <div className="flex flex-wrap gap-1">
                    {news.tags.map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                  <a
                    href={news.source_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-xs text-blue-600 hover:text-blue-800"
                    onClick={(e) => e.stopPropagation()}
                  >
                    查看原文 →
                  </a>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 导入说明 */}
      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div className="flex">
          <CheckCircleIcon className="h-5 w-5 text-yellow-400 mr-2" />
          <div className="text-sm text-yellow-800">
            <p className="font-medium mb-1">导入说明：</p>
            <ul className="list-disc list-inside space-y-1">
              <li>新闻内容已经过整理和格式化，可直接导入使用</li>
              <li>管理员导入的新闻将直接发布，编辑导入的新闻需要审批</li>
              <li>导入后可以在新闻管理页面进一步编辑和管理</li>
              <li>每条新闻都包含原文链接，便于核实信息来源</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
