'use client';

import { useState, useEffect } from 'react';
import { newsApi } from '@/lib/api';
import { newsEvents, eventBus, EVENTS } from '@/lib/eventBus';
import { useAuth } from '@/contexts/AuthContext';
import { 
  PlayIcon, 
  StopIcon, 
  CheckCircleIcon, 
  XCircleIcon,
  ClockIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import type { CreateNewsData, NewsWithDetails } from '@/types';

export default function SyncTest() {
  const [testing, setTesting] = useState(false);
  const [events, setEvents] = useState<Array<{
    type: string;
    data: any;
    timestamp: string;
  }>>([]);
  const [publishedCount, setPublishedCount] = useState(0);
  const [lastUpdate, setLastUpdate] = useState<string>('');
  const { user, isAdmin } = useAuth();

  useEffect(() => {
    // 监听所有新闻相关事件
    const handleEvent = (eventType: string) => (data: any) => {
      const newEvent = {
        type: eventType,
        data,
        timestamp: new Date().toLocaleString()
      };
      setEvents(prev => [newEvent, ...prev.slice(0, 9)]); // 保留最近10条事件
      setLastUpdate(new Date().toLocaleString());
    };

    // 订阅所有事件
    eventBus.on(EVENTS.NEWS_CREATED, handleEvent('新闻创建'));
    eventBus.on(EVENTS.NEWS_UPDATED, handleEvent('新闻更新'));
    eventBus.on(EVENTS.NEWS_DELETED, handleEvent('新闻删除'));
    eventBus.on(EVENTS.NEWS_APPROVED, handleEvent('新闻审批'));
    eventBus.on(EVENTS.NEWS_REFRESH_NEEDED, handleEvent('刷新请求'));

    return () => {
      // 清理事件监听器
      eventBus.clear();
    };
  }, []);

  const startSyncTest = async () => {
    setTesting(true);
    setEvents([]);
    
    try {
      console.log('开始同步测试...');
      
      // 创建测试新闻
      const testNews: CreateNewsData = {
        title: `同步测试新闻 - ${new Date().toLocaleString()}`,
        content: `<h2>同步测试</h2>
        <p>这是一条用于测试数据同步的新闻。</p>
        <h3>测试信息</h3>
        <ul>
          <li>创建时间: ${new Date().toLocaleString()}</li>
          <li>测试用户: ${user?.name || '未知'}</li>
          <li>用户角色: ${user?.role || '未知'}</li>
          <li>预期状态: ${isAdmin() ? 'published' : 'pending'}</li>
        </ul>
        <p>如果同步正常，这条新闻应该立即出现在首页（如果是已发布状态）。</p>`,
        excerpt: '这是一条用于测试数据同步功能的新闻，验证导入后是否能立即在首页显示。',
        category_id: '1',
        status: isAdmin() ? 'published' : 'pending',
        tags: ['同步测试', '实时更新'],
      };

      console.log('创建测试新闻:', testNews);
      const createdNews = await newsApi.createNews(testNews);
      console.log('新闻创建成功:', createdNews);

      // 等待一下，然后检查已发布新闻数量
      setTimeout(async () => {
        const publishedNews = await newsApi.getNews({ status: 'published', limit: 50 });
        setPublishedCount(publishedNews.length);
      }, 1000);

    } catch (error) {
      console.error('同步测试失败:', error);
    } finally {
      setTesting(false);
    }
  };

  const stopTest = () => {
    setTesting(false);
    setEvents([]);
  };

  const refreshPublishedCount = async () => {
    try {
      const publishedNews = await newsApi.getNews({ status: 'published', limit: 50 });
      setPublishedCount(publishedNews.length);
      setLastUpdate(new Date().toLocaleString());
    } catch (error) {
      console.error('刷新失败:', error);
    }
  };

  useEffect(() => {
    // 初始加载已发布新闻数量
    refreshPublishedCount();
  }, []);

  return (
    <div className="max-w-6xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">实时同步测试</h2>
      
      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="text-lg font-medium text-blue-900 mb-2">测试说明</h3>
        <p className="text-sm text-blue-800">
          这个工具用于测试新闻数据的实时同步功能。创建新闻后，应该立即触发事件通知首页刷新数据。
        </p>
      </div>

      {/* 用户信息 */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-medium text-gray-900 mb-2">当前用户</h3>
        <div className="text-sm text-gray-700 space-y-1">
          <p><strong>用户:</strong> {user?.name || '未登录'}</p>
          <p><strong>角色:</strong> {user?.role || '无'}</p>
          <p><strong>是否管理员:</strong> {isAdmin() ? '是' : '否'}</p>
          <p><strong>创建的新闻状态:</strong> {isAdmin() ? 'published (会显示在首页)' : 'pending (需要审批)'}</p>
        </div>
      </div>

      {/* 控制按钮 */}
      <div className="flex space-x-4 mb-6">
        <button
          onClick={startSyncTest}
          disabled={testing}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <PlayIcon className="h-4 w-4 mr-2" />
          {testing ? '测试中...' : '开始同步测试'}
        </button>
        
        <button
          onClick={stopTest}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <StopIcon className="h-4 w-4 mr-2" />
          停止测试
        </button>

        <button
          onClick={refreshPublishedCount}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <ArrowPathIcon className="h-4 w-4 mr-2" />
          刷新统计
        </button>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="p-4 bg-green-50 rounded-lg">
          <h4 className="text-lg font-medium text-green-900">已发布新闻</h4>
          <p className="text-2xl font-bold text-green-700">{publishedCount} 条</p>
          <p className="text-sm text-green-600">首页显示的新闻数量</p>
        </div>
        
        <div className="p-4 bg-blue-50 rounded-lg">
          <h4 className="text-lg font-medium text-blue-900">事件数量</h4>
          <p className="text-2xl font-bold text-blue-700">{events.length} 个</p>
          <p className="text-sm text-blue-600">监听到的事件数量</p>
        </div>
        
        <div className="p-4 bg-gray-50 rounded-lg">
          <h4 className="text-lg font-medium text-gray-900">最后更新</h4>
          <p className="text-sm font-medium text-gray-700">{lastUpdate || '未更新'}</p>
          <p className="text-sm text-gray-600">数据最后更新时间</p>
        </div>
      </div>

      {/* 事件日志 */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">事件日志</h3>
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {events.length === 0 ? (
            <p className="text-gray-500 text-center py-4">暂无事件记录</p>
          ) : (
            events.map((event, index) => (
              <div key={index} className="flex items-start space-x-3 p-3 border border-gray-200 rounded-lg bg-gray-50">
                <div className="flex-shrink-0">
                  {event.type === '新闻创建' && <CheckCircleIcon className="h-5 w-5 text-green-500" />}
                  {event.type === '新闻更新' && <ArrowPathIcon className="h-5 w-5 text-blue-500" />}
                  {event.type === '新闻删除' && <XCircleIcon className="h-5 w-5 text-red-500" />}
                  {event.type === '新闻审批' && <CheckCircleIcon className="h-5 w-5 text-purple-500" />}
                  {event.type === '刷新请求' && <ClockIcon className="h-5 w-5 text-orange-500" />}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-900">{event.type}</h4>
                    <span className="text-xs text-gray-500">{event.timestamp}</span>
                  </div>
                  <p className="text-xs text-gray-600 mt-1">
                    {event.type === '新闻创建' && `标题: ${event.data?.title || '未知'}`}
                    {event.type === '新闻更新' && `ID: ${event.data?.id || '未知'}`}
                    {event.type === '新闻删除' && `ID: ${event.data || '未知'}`}
                    {event.type === '新闻审批' && `标题: ${event.data?.title || '未知'}`}
                    {event.type === '刷新请求' && '触发首页数据刷新'}
                  </p>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* 验证步骤 */}
      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h4 className="text-lg font-medium text-yellow-900 mb-2">验证步骤</h4>
        <ol className="text-sm text-yellow-800 space-y-1 list-decimal list-inside">
          <li>确认使用管理员账户登录（这样创建的新闻会直接发布）</li>
          <li>点击"开始同步测试"创建测试新闻</li>
          <li>观察事件日志，应该看到"新闻创建"和"刷新请求"事件</li>
          <li>打开新的浏览器窗口（未登录状态）访问首页</li>
          <li>检查首页是否显示刚创建的测试新闻</li>
          <li>如果没有显示，点击首页的刷新按钮</li>
        </ol>
      </div>
    </div>
  );
}
