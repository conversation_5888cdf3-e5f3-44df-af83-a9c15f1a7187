'use client';

import { useState } from 'react';
import { newsApi } from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';
import type { CreateNewsData } from '@/types';

export default function QuickImportTest() {
  const [importing, setImporting] = useState(false);
  const [result, setResult] = useState<string>('');
  const { user, isAdmin } = useAuth();

  const testNews: CreateNewsData = {
    title: '测试新闻：荣联科技导入功能验证',
    content: `<h2>测试内容</h2>
    <p>这是一条测试新闻，用于验证导入功能是否正常工作。</p>
    <h3>测试信息</h3>
    <ul>
      <li>导入时间: ${new Date().toLocaleString()}</li>
      <li>导入用户: ${user?.name || '未知'}</li>
      <li>用户角色: ${user?.role || '未知'}</li>
      <li>是否管理员: ${isAdmin() ? '是' : '否'}</li>
    </ul>
    <p>如果您能在首页看到这条新闻，说明导入功能正常工作。</p>`,
    excerpt: '这是一条测试新闻，用于验证荣联科技新闻导入功能是否正常工作。',
    category_id: '1', // 公司新闻
    status: 'published', // 直接设置为已发布
    tags: ['测试', '导入功能', '验证'],
  };

  const handleQuickImport = async () => {
    setImporting(true);
    setResult('');

    try {
      console.log('开始导入测试新闻...');
      console.log('当前用户:', user);
      console.log('是否管理员:', isAdmin());
      console.log('新闻数据:', testNews);

      const createdNews = await newsApi.createNews(testNews);
      
      console.log('导入成功，创建的新闻:', createdNews);
      
      setResult(`✅ 导入成功！
      
新闻ID: ${createdNews.id}
标题: ${createdNews.title}
状态: ${createdNews.status}
创建时间: ${createdNews.created_at}
发布时间: ${createdNews.published_at || '未发布'}

请检查：
1. 在"调试信息"页面查看新闻是否出现在列表中
2. 在首页查看新闻是否显示（可能需要刷新）
3. 确认新闻状态为 'published'`);

    } catch (error: any) {
      console.error('导入失败:', error);
      setResult(`❌ 导入失败: ${error.message || '未知错误'}`);
    } finally {
      setImporting(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">快速导入测试</h2>
      
      <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 className="text-lg font-medium text-yellow-900 mb-2">测试说明</h3>
        <p className="text-sm text-yellow-800">
          这个功能会创建一条测试新闻来验证导入功能是否正常工作。
          测试新闻会直接设置为 'published' 状态，应该能在首页看到。
        </p>
      </div>

      {/* 用户信息 */}
      <div className="mb-6 p-4 bg-blue-50 rounded-lg">
        <h3 className="text-lg font-medium text-blue-900 mb-2">当前用户信息</h3>
        <div className="text-sm text-blue-800 space-y-1">
          <p><strong>用户:</strong> {user?.name || '未登录'}</p>
          <p><strong>邮箱:</strong> {user?.email || '无'}</p>
          <p><strong>角色:</strong> {user?.role || '无'}</p>
          <p><strong>是否管理员:</strong> {isAdmin() ? '是' : '否'}</p>
        </div>
      </div>

      {/* 测试新闻预览 */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-medium text-gray-900 mb-2">测试新闻预览</h3>
        <div className="text-sm text-gray-700 space-y-2">
          <p><strong>标题:</strong> {testNews.title}</p>
          <p><strong>摘要:</strong> {testNews.excerpt}</p>
          <p><strong>分类ID:</strong> {testNews.category_id}</p>
          <p><strong>状态:</strong> {testNews.status}</p>
          <p><strong>标签:</strong> {testNews.tags?.join(', ')}</p>
        </div>
      </div>

      {/* 导入按钮 */}
      <div className="mb-6">
        <button
          onClick={handleQuickImport}
          disabled={importing}
          className="w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {importing ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              导入中...
            </>
          ) : (
            '开始快速导入测试'
          )}
        </button>
      </div>

      {/* 结果显示 */}
      {result && (
        <div className="p-4 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-medium text-gray-900 mb-2">导入结果</h3>
          <pre className="text-sm text-gray-700 whitespace-pre-wrap">{result}</pre>
        </div>
      )}

      {/* 验证步骤 */}
      <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
        <h3 className="text-lg font-medium text-green-900 mb-2">验证步骤</h3>
        <ol className="text-sm text-green-800 space-y-1 list-decimal list-inside">
          <li>点击上方的"开始快速导入测试"按钮</li>
          <li>等待导入完成，查看结果信息</li>
          <li>前往"调试信息"页面，确认新闻出现在列表中</li>
          <li>检查新闻状态是否为 'published'</li>
          <li>打开新的浏览器窗口或退出登录</li>
          <li>访问首页，查看测试新闻是否显示</li>
          <li>如果首页没有显示，点击刷新按钮</li>
        </ol>
      </div>
    </div>
  );
}
