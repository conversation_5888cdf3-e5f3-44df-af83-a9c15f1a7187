'use client';

import { useState } from 'react';
import { ArrowPathIcon, CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/outline';
import { newsApi } from '@/lib/api';
import type { NewsWithDetails } from '@/types';

export default function RefreshTest() {
  const [testing, setTesting] = useState(false);
  const [results, setResults] = useState<{
    allNews: NewsWithDetails[];
    publishedNews: NewsWithDetails[];
    timestamp: string;
  } | null>(null);
  const [error, setError] = useState<string>('');

  const runRefreshTest = async () => {
    setTesting(true);
    setError('');
    setResults(null);

    try {
      console.log('开始刷新测试...');
      
      // 获取所有新闻
      const allNews = await newsApi.getNews({ limit: 50 });
      console.log('获取所有新闻:', allNews.length, '条');
      
      // 获取已发布的新闻
      const publishedNews = await newsApi.getNews({ status: 'published', limit: 50 });
      console.log('获取已发布新闻:', publishedNews.length, '条');
      
      setResults({
        allNews,
        publishedNews,
        timestamp: new Date().toLocaleString()
      });
      
    } catch (err: any) {
      console.error('刷新测试失败:', err);
      setError(err.message || '测试失败');
    } finally {
      setTesting(false);
    }
  };

  const clearCache = () => {
    // 清除可能的缓存
    if (typeof window !== 'undefined') {
      // 强制刷新页面
      window.location.reload();
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">刷新功能测试</h2>
      
      <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="text-lg font-medium text-blue-900 mb-2">测试说明</h3>
        <p className="text-sm text-blue-800">
          这个工具用于测试数据刷新功能，检查新闻数据是否能正确加载和更新。
        </p>
      </div>

      {/* 测试按钮 */}
      <div className="flex space-x-4 mb-6">
        <button
          onClick={runRefreshTest}
          disabled={testing}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ArrowPathIcon className={`h-4 w-4 mr-2 ${testing ? 'animate-spin' : ''}`} />
          {testing ? '测试中...' : '运行刷新测试'}
        </button>
        
        <button
          onClick={clearCache}
          className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <XCircleIcon className="h-4 w-4 mr-2" />
          清除缓存并刷新页面
        </button>
      </div>

      {/* 错误信息 */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex">
            <XCircleIcon className="h-5 w-5 text-red-400 mr-2" />
            <p className="text-sm text-red-800">{error}</p>
          </div>
        </div>
      )}

      {/* 测试结果 */}
      {results && (
        <div className="space-y-6">
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center mb-2">
              <CheckCircleIcon className="h-5 w-5 text-green-400 mr-2" />
              <h3 className="text-lg font-medium text-green-900">测试完成</h3>
            </div>
            <p className="text-sm text-green-800">
              测试时间: {results.timestamp}
            </p>
          </div>

          {/* 数据统计 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="p-4 bg-gray-50 rounded-lg">
              <h4 className="text-lg font-medium text-gray-900 mb-2">所有新闻</h4>
              <p className="text-2xl font-bold text-gray-700">{results.allNews.length} 条</p>
              <div className="mt-2 text-sm text-gray-600">
                {Object.entries(results.allNews.reduce((acc, news) => {
                  acc[news.status] = (acc[news.status] || 0) + 1;
                  return acc;
                }, {} as Record<string, number>)).map(([status, count]) => (
                  <div key={status} className="flex justify-between">
                    <span>{status}:</span>
                    <span>{count}</span>
                  </div>
                ))}
              </div>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="text-lg font-medium text-green-900 mb-2">已发布新闻</h4>
              <p className="text-2xl font-bold text-green-700">{results.publishedNews.length} 条</p>
              <p className="text-sm text-green-600">这些新闻会显示在首页</p>
            </div>
          </div>

          {/* 已发布新闻列表 */}
          <div>
            <h4 className="text-lg font-medium text-gray-900 mb-4">已发布新闻列表</h4>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {results.publishedNews.length === 0 ? (
                <p className="text-gray-500 text-center py-4">没有已发布的新闻</p>
              ) : (
                results.publishedNews.map((news) => (
                  <div key={news.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg bg-white">
                    <div className="flex-1">
                      <h5 className="font-medium text-gray-900">{news.title}</h5>
                      <p className="text-sm text-gray-600">
                        作者: {news.author.name} | 分类: {news.category.name} | 
                        创建: {new Date(news.created_at).toLocaleString()}
                      </p>
                    </div>
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      {news.status}
                    </span>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* 操作建议 */}
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 className="text-lg font-medium text-yellow-900 mb-2">下一步操作</h4>
            <ol className="text-sm text-yellow-800 space-y-1 list-decimal list-inside">
              <li>如果已发布新闻数量为 0，请先导入一些新闻</li>
              <li>打开新的浏览器窗口（未登录状态）</li>
              <li>访问首页 http://localhost:3000</li>
              <li>检查是否显示上述已发布的新闻</li>
              <li>如果没有显示，点击首页的刷新按钮（🔄）</li>
              <li>如果仍然没有显示，使用"清除缓存并刷新页面"按钮</li>
            </ol>
          </div>
        </div>
      )}

      {/* 使用说明 */}
      <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <h4 className="text-lg font-medium text-gray-900 mb-2">使用说明</h4>
        <div className="text-sm text-gray-700 space-y-2">
          <p><strong>运行刷新测试:</strong> 获取最新的新闻数据，查看当前状态</p>
          <p><strong>清除缓存并刷新:</strong> 强制刷新整个页面，清除所有缓存</p>
          <p><strong>控制台日志:</strong> 打开浏览器开发者工具查看详细的加载日志</p>
        </div>
      </div>
    </div>
  );
}
