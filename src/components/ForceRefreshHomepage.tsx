'use client';

import { useState } from 'react';
import { newsEvents } from '@/lib/eventBus';
import { 
  ArrowPathIcon, 
  GlobeAltIcon, 
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

export default function ForceRefreshHomepage() {
  const [refreshing, setRefreshing] = useState(false);
  const [message, setMessage] = useState<string>('');

  const forceRefreshHomepage = () => {
    setRefreshing(true);
    setMessage('');

    try {
      console.log('强制触发首页刷新事件...');
      
      // 触发刷新事件
      newsEvents.notifyNewsCreated({
        id: 'force-refresh',
        title: '强制刷新触发器',
        status: 'published'
      });

      setMessage('✅ 已触发首页刷新事件！请检查首页是否更新。');
      
      setTimeout(() => {
        setMessage('');
      }, 5000);

    } catch (error) {
      console.error('触发刷新失败:', error);
      setMessage('❌ 触发刷新失败，请重试。');
      
      setTimeout(() => {
        setMessage('');
      }, 5000);
    } finally {
      setRefreshing(false);
    }
  };

  const openHomepage = () => {
    window.open('/', '_blank');
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">强制刷新首页</h2>
      
      <div className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg">
        <div className="flex">
          <ExclamationTriangleIcon className="h-5 w-5 text-orange-400 mr-2" />
          <div>
            <h3 className="text-lg font-medium text-orange-900 mb-2">使用说明</h3>
            <p className="text-sm text-orange-800">
              如果导入新闻后首页没有显示，可以使用这个工具强制触发首页刷新事件。
              这会通知所有监听的组件重新加载数据。
            </p>
          </div>
        </div>
      </div>

      {/* 操作按钮 */}
      <div className="flex space-x-4 mb-6">
        <button
          onClick={forceRefreshHomepage}
          disabled={refreshing}
          className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <ArrowPathIcon className={`h-5 w-5 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          {refreshing ? '触发中...' : '强制刷新首页'}
        </button>
        
        <button
          onClick={openHomepage}
          className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <GlobeAltIcon className="h-5 w-5 mr-2" />
          打开首页
        </button>
      </div>

      {/* 结果消息 */}
      {message && (
        <div className={`p-4 rounded-lg mb-6 ${
          message.startsWith('✅') 
            ? 'bg-green-50 border border-green-200' 
            : 'bg-red-50 border border-red-200'
        }`}>
          <div className="flex">
            <CheckCircleIcon className={`h-5 w-5 mr-2 ${
              message.startsWith('✅') ? 'text-green-400' : 'text-red-400'
            }`} />
            <p className={`text-sm ${
              message.startsWith('✅') ? 'text-green-800' : 'text-red-800'
            }`}>
              {message}
            </p>
          </div>
        </div>
      )}

      {/* 使用步骤 */}
      <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h3 className="text-lg font-medium text-blue-900 mb-2">使用步骤</h3>
        <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
          <li>确认已经导入了一些新闻（使用管理员账户）</li>
          <li>点击"强制刷新首页"按钮触发刷新事件</li>
          <li>点击"打开首页"按钮在新窗口中打开首页</li>
          <li>检查首页是否显示最新导入的新闻</li>
          <li>如果仍然没有显示，可以在首页点击刷新按钮</li>
        </ol>
      </div>

      {/* 技术说明 */}
      <div className="mt-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
        <h3 className="text-lg font-medium text-gray-900 mb-2">技术说明</h3>
        <div className="text-sm text-gray-700 space-y-2">
          <p><strong>工作原理:</strong> 通过事件总线触发 NEWS_REFRESH_NEEDED 事件</p>
          <p><strong>监听组件:</strong> PublicNewsDisplay 组件会监听此事件并刷新数据</p>
          <p><strong>实时性:</strong> 事件触发后，首页应该立即开始重新加载数据</p>
          <p><strong>调试:</strong> 打开浏览器控制台可以看到详细的事件日志</p>
        </div>
      </div>

      {/* 故障排除 */}
      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h3 className="text-lg font-medium text-yellow-900 mb-2">故障排除</h3>
        <div className="text-sm text-yellow-800 space-y-2">
          <p><strong>如果首页仍然没有更新:</strong></p>
          <ul className="list-disc list-inside ml-4 space-y-1">
            <li>检查导入的新闻状态是否为 'published'</li>
            <li>使用"调试信息"页面查看新闻统计</li>
            <li>在首页手动点击刷新按钮</li>
            <li>清除浏览器缓存并刷新页面</li>
            <li>检查控制台是否有错误信息</li>
          </ul>
        </div>
      </div>
    </div>
  );
}
