'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import RichTextEditor from './RichTextEditor';
import { categoryApi } from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';
import type { Category, CreateNewsData, News } from '@/types';

const newsSchema = z.object({
  title: z.string().min(1, '标题不能为空').max(200, '标题不能超过200个字符'),
  excerpt: z.string().min(1, '摘要不能为空').max(500, '摘要不能超过500个字符'),
  content: z.string().min(1, '内容不能为空'),
  category_id: z.string().min(1, '请选择分类'),
  status: z.enum(['draft', 'pending', 'published']),
  tags: z.string().optional(),
});

type NewsFormData = z.infer<typeof newsSchema>;

interface NewsFormProps {
  initialData?: News;
  onSubmit: (data: CreateNewsData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export default function NewsForm({ initialData, onSubmit, onCancel, isLoading }: NewsFormProps) {
  const [categories, setCategories] = useState<Category[]>([]);
  const [content, setContent] = useState(initialData?.content || '');
  const { isAdmin } = useAuth();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm<NewsFormData>({
    resolver: zodResolver(newsSchema),
    defaultValues: {
      title: initialData?.title || '',
      excerpt: initialData?.excerpt || '',
      content: initialData?.content || '',
      category_id: initialData?.category_id || '',
      status: initialData?.status || 'draft',
      tags: initialData?.tags?.join(', ') || '',
    },
  });

  useEffect(() => {
    loadCategories();
  }, []);

  useEffect(() => {
    setValue('content', content);
  }, [content, setValue]);

  const loadCategories = async () => {
    try {
      const data = await categoryApi.getCategories();
      setCategories(data);
    } catch (error) {
      console.error('加载分类失败:', error);
    }
  };

  const onFormSubmit = async (data: NewsFormData) => {
    const tags = data.tags ? data.tags.split(',').map(tag => tag.trim()).filter(Boolean) : [];
    
    await onSubmit({
      ...data,
      tags,
    });
  };

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold text-gray-900 mb-6">
        {initialData ? '编辑新闻' : '创建新闻'}
      </h2>

      <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
        {/* 标题 */}
        <div>
          <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
            标题 *
          </label>
          <input
            type="text"
            id="title"
            {...register('title')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入新闻标题"
          />
          {errors.title && (
            <p className="mt-1 text-sm text-red-600">{errors.title.message}</p>
          )}
        </div>

        {/* 摘要 */}
        <div>
          <label htmlFor="excerpt" className="block text-sm font-medium text-gray-700 mb-2">
            摘要 *
          </label>
          <textarea
            id="excerpt"
            rows={3}
            {...register('excerpt')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入新闻摘要"
          />
          {errors.excerpt && (
            <p className="mt-1 text-sm text-red-600">{errors.excerpt.message}</p>
          )}
        </div>

        {/* 分类 */}
        <div>
          <label htmlFor="category_id" className="block text-sm font-medium text-gray-700 mb-2">
            分类 *
          </label>
          <select
            id="category_id"
            {...register('category_id')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">请选择分类</option>
            {categories.map((category) => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
          {errors.category_id && (
            <p className="mt-1 text-sm text-red-600">{errors.category_id.message}</p>
          )}
        </div>

        {/* 标签 */}
        <div>
          <label htmlFor="tags" className="block text-sm font-medium text-gray-700 mb-2">
            标签
          </label>
          <input
            type="text"
            id="tags"
            {...register('tags')}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入标签，用逗号分隔"
          />
          <p className="mt-1 text-sm text-gray-500">多个标签请用逗号分隔</p>
        </div>

        {/* 内容 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            内容 *
          </label>
          <RichTextEditor
            content={content}
            onChange={setContent}
            placeholder="请输入新闻内容..."
          />
          {errors.content && (
            <p className="mt-1 text-sm text-red-600">{errors.content.message}</p>
          )}
        </div>

        {/* 状态 */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            状态
          </label>
          <div className="flex space-x-4">
            <label className="flex items-center">
              <input
                type="radio"
                value="draft"
                {...register('status')}
                className="mr-2"
              />
              草稿
            </label>
            {isAdmin() && (
              <label className="flex items-center">
                <input
                  type="radio"
                  value="published"
                  {...register('status')}
                  className="mr-2"
                />
                直接发布
              </label>
            )}
            <label className="flex items-center">
              <input
                type="radio"
                value={isAdmin() ? "pending" : "published"}
                {...register('status')}
                className="mr-2"
              />
              {isAdmin() ? "待审批" : "提交发布"}
            </label>
          </div>
          {!isAdmin() && (
            <p className="mt-1 text-sm text-gray-500">
              选择"提交发布"将提交给管理员审批
            </p>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onCancel}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            取消
          </button>
          <button
            type="submit"
            disabled={isLoading}
            className="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isLoading ? '保存中...' : (initialData ? '更新' : '创建')}
          </button>
        </div>
      </form>
    </div>
  );
}
