<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="handleBackdropClick">
    <div class="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden" @click.stop>
      <!-- 头部 -->
      <div class="flex justify-between items-center p-6 border-b border-gray-200">
        <div>
          <h2 class="text-xl font-bold text-gray-900">API 测试器</h2>
          <p class="text-sm text-gray-600 mt-1">
            <span 
              class="inline-flex items-center px-2 py-1 rounded text-xs font-medium mr-2"
              :class="getMethodClass(endpoint?.method || 'GET')"
            >
              {{ endpoint?.method || 'GET' }}
            </span>
            {{ endpoint?.path || '/api/test' }}
          </p>
        </div>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 内容 -->
      <div class="flex h-[calc(90vh-140px)]">
        <!-- 左侧：请求配置 -->
        <div class="w-1/2 p-6 border-r border-gray-200 overflow-y-auto">
          <div class="space-y-6">
            <!-- 基本信息 -->
            <div>
              <h3 class="text-lg font-semibold text-gray-900 mb-4">请求配置</h3>
              
              <!-- URL -->
              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">请求URL</label>
                <div class="flex">
                  <select
                    v-model="baseUrl"
                    class="px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-gray-50"
                  >
                    <option value="https://api.ronglian.com">生产环境</option>
                    <option value="https://test-api.ronglian.com">测试环境</option>
                    <option value="http://localhost:3000">本地环境</option>
                  </select>
                  <input
                    v-model="requestPath"
                    type="text"
                    class="flex-1 px-3 py-2 border-l-0 border border-gray-300 rounded-r-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="/api/endpoint"
                  />
                </div>
              </div>

              <!-- 请求方法 -->
              <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">请求方法</label>
                <select
                  v-model="requestMethod"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="GET">GET</option>
                  <option value="POST">POST</option>
                  <option value="PUT">PUT</option>
                  <option value="DELETE">DELETE</option>
                  <option value="PATCH">PATCH</option>
                </select>
              </div>
            </div>

            <!-- 请求头 -->
            <div>
              <h4 class="text-sm font-medium text-gray-900 mb-3">请求头</h4>
              <div class="space-y-2">
                <div
                  v-for="(header, index) in headers"
                  :key="index"
                  class="flex space-x-2"
                >
                  <input
                    v-model="header.key"
                    type="text"
                    placeholder="Header名称"
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                  />
                  <input
                    v-model="header.value"
                    type="text"
                    placeholder="Header值"
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                  />
                  <button
                    @click="removeHeader(index)"
                    class="px-2 py-2 text-red-600 hover:text-red-800"
                  >
                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
                <button
                  @click="addHeader"
                  class="text-sm text-blue-600 hover:text-blue-800"
                >
                  + 添加请求头
                </button>
              </div>
            </div>

            <!-- 查询参数 -->
            <div v-if="requestMethod === 'GET'">
              <h4 class="text-sm font-medium text-gray-900 mb-3">查询参数</h4>
              <div class="space-y-2">
                <div
                  v-for="(param, index) in queryParams"
                  :key="index"
                  class="flex space-x-2"
                >
                  <input
                    v-model="param.key"
                    type="text"
                    placeholder="参数名"
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                  />
                  <input
                    v-model="param.value"
                    type="text"
                    placeholder="参数值"
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                  />
                  <button
                    @click="removeQueryParam(index)"
                    class="px-2 py-2 text-red-600 hover:text-red-800"
                  >
                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
                <button
                  @click="addQueryParam"
                  class="text-sm text-blue-600 hover:text-blue-800"
                >
                  + 添加查询参数
                </button>
              </div>
            </div>

            <!-- 请求体 -->
            <div v-if="['POST', 'PUT', 'PATCH'].includes(requestMethod)">
              <h4 class="text-sm font-medium text-gray-900 mb-3">请求体</h4>
              <div class="mb-2">
                <select
                  v-model="bodyType"
                  class="px-3 py-1 border border-gray-300 rounded text-sm"
                >
                  <option value="json">JSON</option>
                  <option value="form">Form Data</option>
                  <option value="text">Raw Text</option>
                </select>
              </div>
              
              <textarea
                v-if="bodyType === 'json' || bodyType === 'text'"
                v-model="requestBody"
                rows="8"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 font-mono text-sm"
                :placeholder="bodyType === 'json' ? '{\n  \"key\": \"value\"\n}' : '请输入请求体内容'"
              ></textarea>
              
              <div v-else class="space-y-2">
                <div
                  v-for="(field, index) in formData"
                  :key="index"
                  class="flex space-x-2"
                >
                  <input
                    v-model="field.key"
                    type="text"
                    placeholder="字段名"
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                  />
                  <input
                    v-model="field.value"
                    type="text"
                    placeholder="字段值"
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                  />
                  <button
                    @click="removeFormField(index)"
                    class="px-2 py-2 text-red-600 hover:text-red-800"
                  >
                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
                <button
                  @click="addFormField"
                  class="text-sm text-blue-600 hover:text-blue-800"
                >
                  + 添加表单字段
                </button>
              </div>
            </div>

            <!-- 发送按钮 -->
            <div class="pt-4">
              <button
                @click="sendRequest"
                :disabled="loading"
                class="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                <svg v-if="loading" class="animate-spin -ml-1 mr-3 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ loading ? '发送中...' : '发送请求' }}
              </button>
            </div>
          </div>
        </div>

        <!-- 右侧：响应结果 -->
        <div class="w-1/2 p-6 overflow-y-auto">
          <div class="space-y-4">
            <h3 class="text-lg font-semibold text-gray-900">响应结果</h3>
            
            <!-- 响应状态 -->
            <div v-if="response" class="space-y-4">
              <div class="flex items-center space-x-4">
                <span class="text-sm font-medium text-gray-700">状态码:</span>
                <span 
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getStatusClass(response.status.toString())"
                >
                  {{ response.status }} {{ response.statusText }}
                </span>
                <span class="text-sm text-gray-500">{{ response.duration }}ms</span>
              </div>

              <!-- 响应头 -->
              <div>
                <h4 class="text-sm font-medium text-gray-900 mb-2">响应头</h4>
                <div class="bg-gray-50 rounded-lg p-3 max-h-32 overflow-y-auto">
                  <pre class="text-xs text-gray-700">{{ formatHeaders(response.headers) }}</pre>
                </div>
              </div>

              <!-- 响应体 -->
              <div>
                <h4 class="text-sm font-medium text-gray-900 mb-2">响应体</h4>
                <div class="bg-gray-900 rounded-lg p-4 max-h-96 overflow-y-auto">
                  <pre class="text-sm text-green-400">{{ formatResponseBody(response.data) }}</pre>
                </div>
              </div>
            </div>

            <!-- 空状态 -->
            <div v-else class="text-center py-12">
              <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
              <h3 class="mt-2 text-sm font-medium text-gray-900">等待发送请求</h3>
              <p class="mt-1 text-sm text-gray-500">配置请求参数后点击发送按钮</p>
            </div>

            <!-- 错误信息 -->
            <div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
              <div class="flex">
                <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-red-800">请求失败</h3>
                  <p class="mt-1 text-sm text-red-700">{{ error }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineProps, defineEmits } from 'vue'

// Props
interface Props {
  endpoint?: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
  test: [result: any]
}>()

// 响应式数据
const loading = ref(false)
const baseUrl = ref('http://localhost:3000')
const requestPath = ref('')
const requestMethod = ref('GET')
const bodyType = ref('json')
const requestBody = ref('')
const response = ref(null)
const error = ref('')

// 请求头
const headers = ref([
  { key: 'Content-Type', value: 'application/json' },
  { key: 'Authorization', value: 'Bearer your-token-here' }
])

// 查询参数
const queryParams = ref([
  { key: '', value: '' }
])

// 表单数据
const formData = ref([
  { key: '', value: '' }
])

// 方法
const handleBackdropClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    emit('close')
  }
}

const addHeader = () => {
  headers.value.push({ key: '', value: '' })
}

const removeHeader = (index: number) => {
  headers.value.splice(index, 1)
}

const addQueryParam = () => {
  queryParams.value.push({ key: '', value: '' })
}

const removeQueryParam = (index: number) => {
  queryParams.value.splice(index, 1)
}

const addFormField = () => {
  formData.value.push({ key: '', value: '' })
}

const removeFormField = (index: number) => {
  formData.value.splice(index, 1)
}

const getMethodClass = (method: string) => {
  switch (method.toUpperCase()) {
    case 'GET':
      return 'bg-green-100 text-green-800'
    case 'POST':
      return 'bg-blue-100 text-blue-800'
    case 'PUT':
      return 'bg-yellow-100 text-yellow-800'
    case 'DELETE':
      return 'bg-red-100 text-red-800'
    case 'PATCH':
      return 'bg-purple-100 text-purple-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusClass = (code: string) => {
  const statusCode = parseInt(code)
  if (statusCode >= 200 && statusCode < 300) {
    return 'bg-green-100 text-green-800'
  } else if (statusCode >= 400 && statusCode < 500) {
    return 'bg-yellow-100 text-yellow-800'
  } else if (statusCode >= 500) {
    return 'bg-red-100 text-red-800'
  }
  return 'bg-gray-100 text-gray-800'
}

const formatHeaders = (headers: any) => {
  if (!headers) return ''
  return Object.entries(headers)
    .map(([key, value]) => `${key}: ${value}`)
    .join('\n')
}

const formatResponseBody = (data: any) => {
  if (!data) return ''
  if (typeof data === 'string') return data
  return JSON.stringify(data, null, 2)
}

const buildUrl = () => {
  let url = baseUrl.value + requestPath.value

  if (requestMethod.value === 'GET' && queryParams.value.length > 0) {
    const params = queryParams.value
      .filter(param => param.key && param.value)
      .map(param => `${encodeURIComponent(param.key)}=${encodeURIComponent(param.value)}`)
      .join('&')

    if (params) {
      url += '?' + params
    }
  }

  return url
}

const buildHeaders = () => {
  const result: Record<string, string> = {}

  headers.value.forEach(header => {
    if (header.key && header.value) {
      result[header.key] = header.value
    }
  })

  return result
}

const buildBody = () => {
  if (!['POST', 'PUT', 'PATCH'].includes(requestMethod.value)) {
    return undefined
  }

  if (bodyType.value === 'json') {
    try {
      return requestBody.value ? JSON.parse(requestBody.value) : undefined
    } catch (e) {
      throw new Error('JSON格式错误')
    }
  } else if (bodyType.value === 'form') {
    const result: Record<string, string> = {}
    formData.value.forEach(field => {
      if (field.key && field.value) {
        result[field.key] = field.value
      }
    })
    return result
  } else {
    return requestBody.value
  }
}

const sendRequest = async () => {
  loading.value = true
  error.value = ''
  response.value = null

  try {
    const url = buildUrl()
    const requestHeaders = buildHeaders()
    const body = buildBody()

    const startTime = Date.now()

    // 模拟API请求
    const mockResponse = await simulateApiRequest(url, requestMethod.value, requestHeaders, body)

    const endTime = Date.now()
    const duration = endTime - startTime

    response.value = {
      ...mockResponse,
      duration
    }

    emit('test', {
      url,
      method: requestMethod.value,
      headers: requestHeaders,
      body,
      response: response.value
    })

  } catch (err) {
    error.value = (err as Error).message
  } finally {
    loading.value = false
  }
}

// 模拟API请求
const simulateApiRequest = async (url: string, method: string, headers: any, body: any) => {
  // 模拟网络延迟
  await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000))

  // 根据URL和方法返回模拟响应
  if (url.includes('/api/auth/login') && method === 'POST') {
    if (body?.email === '<EMAIL>' && body?.password === 'admin123') {
      return {
        status: 200,
        statusText: 'OK',
        headers: {
          'Content-Type': 'application/json',
          'Set-Cookie': 'session=abc123; HttpOnly'
        },
        data: {
          success: true,
          data: {
            token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxIiwibmFtZSI6Iuezu-e7n-euoeeQhuWRmCIsInJvbGUiOiJhZG1pbiJ9.mock-signature',
            user: {
              id: '1',
              name: '系统管理员',
              email: '<EMAIL>',
              role: 'admin'
            }
          },
          message: '登录成功'
        }
      }
    } else {
      return {
        status: 401,
        statusText: 'Unauthorized',
        headers: {
          'Content-Type': 'application/json'
        },
        data: {
          success: false,
          message: '邮箱或密码错误'
        }
      }
    }
  }

  if (url.includes('/api/news') && method === 'GET') {
    return {
      status: 200,
      statusText: 'OK',
      headers: {
        'Content-Type': 'application/json',
        'X-Total-Count': '50'
      },
      data: {
        success: true,
        data: {
          items: [
            {
              id: '1',
              title: '荣联科技发布新一代AI解决方案',
              excerpt: '本次发布的AI解决方案将为企业数字化转型提供强有力的支持...',
              category: '产品发布',
              status: 'published',
              author: '新闻编辑',
              views: 1250,
              created_at: '2024-12-15T10:00:00Z'
            },
            {
              id: '2',
              title: 'Vue 3.0最佳实践分享',
              excerpt: '分享Vue 3.0开发中的最佳实践和常见问题解决方案...',
              category: '技术分享',
              status: 'published',
              author: '技术团队',
              views: 890,
              created_at: '2024-12-14T14:30:00Z'
            }
          ],
          pagination: {
            current_page: 1,
            total_pages: 5,
            total_items: 50,
            per_page: 10
          }
        }
      }
    }
  }

  if (url.includes('/api/users') && method === 'GET') {
    if (!headers.Authorization || !headers.Authorization.includes('Bearer')) {
      return {
        status: 401,
        statusText: 'Unauthorized',
        headers: {
          'Content-Type': 'application/json'
        },
        data: {
          success: false,
          message: '未授权访问'
        }
      }
    }

    return {
      status: 200,
      statusText: 'OK',
      headers: {
        'Content-Type': 'application/json'
      },
      data: {
        success: true,
        data: {
          items: [
            {
              id: '1',
              name: '系统管理员',
              email: '<EMAIL>',
              role: 'admin',
              status: 'active',
              created_at: '2024-01-01T00:00:00Z'
            },
            {
              id: '2',
              name: '新闻编辑',
              email: '<EMAIL>',
              role: 'editor',
              status: 'active',
              created_at: '2024-02-01T00:00:00Z'
            }
          ],
          pagination: {
            current_page: 1,
            total_pages: 1,
            total_items: 2
          }
        }
      }
    }
  }

  // 默认404响应
  return {
    status: 404,
    statusText: 'Not Found',
    headers: {
      'Content-Type': 'application/json'
    },
    data: {
      success: false,
      message: '接口不存在'
    }
  }
}

// 初始化
const initializeFromEndpoint = () => {
  if (props.endpoint) {
    requestPath.value = props.endpoint.path
    requestMethod.value = props.endpoint.method

    // 设置示例请求体
    if (props.endpoint.requestExample) {
      const lines = props.endpoint.requestExample.split('\n')
      const bodyStartIndex = lines.findIndex(line => line.trim() === '') + 1
      if (bodyStartIndex > 0) {
        const bodyLines = lines.slice(bodyStartIndex)
        requestBody.value = bodyLines.join('\n')
      }
    }

    // 设置认证头
    if (props.endpoint.auth) {
      const authHeader = headers.value.find(h => h.key === 'Authorization')
      if (authHeader) {
        authHeader.value = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'
      }
    }
  }
}

// 生命周期
onMounted(() => {
  initializeFromEndpoint()
})
</script>

<style scoped>
/* 代码块样式 */
pre {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
}

/* 滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 加载动画 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
