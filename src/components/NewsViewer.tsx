'use client';

import { useEffect } from 'react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { 
  XMarkIcon, 
  EyeIcon, 
  CalendarIcon, 
  UserIcon, 
  TagIcon,
  FolderIcon 
} from '@heroicons/react/24/outline';
import { newsApi } from '@/lib/api';
import type { NewsWithDetails } from '@/types';

interface NewsViewerProps {
  news: NewsWithDetails;
  onClose: () => void;
}

export default function NewsViewer({ news, onClose }: NewsViewerProps) {
  // 增加浏览量
  useEffect(() => {
    const incrementView = async () => {
      try {
        await newsApi.incrementViewCount(news.id);
      } catch (error) {
        console.error('增加浏览量失败:', error);
      }
    };

    // 只对已发布的新闻增加浏览量
    if (news.status === 'published') {
      incrementView();
    }
  }, [news.id, news.status]);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { label: '草稿', className: 'bg-gray-100 text-gray-800' },
      pending: { label: '待审批', className: 'bg-yellow-100 text-yellow-800' },
      published: { label: '已发布', className: 'bg-green-100 text-green-800' },
      rejected: { label: '已拒绝', className: 'bg-red-100 text-red-800' },
      archived: { label: '已归档', className: 'bg-purple-100 text-purple-800' },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.className}`}>
        {config.label}
      </span>
    );
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-4 mx-auto p-5 border max-w-4xl shadow-lg rounded-md bg-white mb-8">
        {/* 头部 */}
        <div className="flex justify-between items-start mb-6">
          <div className="flex-1 pr-4">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {news.title}
            </h1>
            <div className="flex items-center space-x-4 text-sm text-gray-500 mb-4">
              {getStatusBadge(news.status)}
              <div className="flex items-center">
                <EyeIcon className="h-4 w-4 mr-1" />
                <span>{news.view_count} 次浏览</span>
              </div>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 p-1"
            title="关闭"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* 新闻元信息 */}
        <div className="border-b border-gray-200 pb-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="flex items-center text-gray-600">
              <UserIcon className="h-4 w-4 mr-2" />
              <span>作者: {news.author.name}</span>
            </div>
            <div className="flex items-center text-gray-600">
              <FolderIcon className="h-4 w-4 mr-2" />
              <span>分类: {news.category.name}</span>
            </div>
            <div className="flex items-center text-gray-600">
              <CalendarIcon className="h-4 w-4 mr-2" />
              <span>
                创建时间: {format(new Date(news.created_at), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })}
              </span>
            </div>
            {news.published_at && (
              <div className="flex items-center text-gray-600">
                <CalendarIcon className="h-4 w-4 mr-2" />
                <span>
                  发布时间: {format(new Date(news.published_at), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })}
                </span>
              </div>
            )}
          </div>

          {/* 标签 */}
          {news.tags && news.tags.length > 0 && (
            <div className="mt-4">
              <div className="flex items-center mb-2">
                <TagIcon className="h-4 w-4 mr-2 text-gray-600" />
                <span className="text-sm text-gray-600">标签:</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {news.tags.map((tag, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {tag}
                  </span>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* 摘要 */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-2">摘要</h3>
          <p className="text-gray-700 leading-relaxed bg-gray-50 p-4 rounded-lg">
            {news.excerpt}
          </p>
        </div>

        {/* 新闻内容 */}
        <div className="mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">正文</h3>
          <div 
            className="prose prose-sm sm:prose lg:prose-lg xl:prose-xl max-w-none"
            dangerouslySetInnerHTML={{ __html: news.content }}
          />
        </div>

        {/* 拒绝理由（如果有） */}
        {news.status === 'rejected' && news.rejection_reason && (
          <div className="mb-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-red-800 mb-2">拒绝理由</h4>
              <p className="text-sm text-red-700">{news.rejection_reason}</p>
            </div>
          </div>
        )}

        {/* 底部操作栏 */}
        <div className="border-t border-gray-200 pt-4">
          <div className="flex justify-between items-center">
            <div className="text-sm text-gray-500">
              最后更新: {format(new Date(news.updated_at), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })}
            </div>
            <button
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              关闭
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
