'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { CheckIcon, XMarkIcon, EyeIcon } from '@heroicons/react/24/outline';
import { newsApi } from '@/lib/api';
import { useAuth } from '@/contexts/AuthContext';
import type { NewsWithDetails } from '@/types';

export default function NewsApproval() {
  const [pendingNews, setPendingNews] = useState<NewsWithDetails[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedNews, setSelectedNews] = useState<NewsWithDetails | null>(null);
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectModal, setShowRejectModal] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const { isAdmin } = useAuth();

  useEffect(() => {
    if (isAdmin()) {
      loadPendingNews();
    }
  }, [refreshTrigger]);

  const loadPendingNews = async () => {
    try {
      setLoading(true);
      const data = await newsApi.getNews({ status: 'pending' });
      setPendingNews(data);
    } catch (error) {
      console.error('加载待审批新闻失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleApprove = async (newsId: string) => {
    if (!confirm('确定要批准这条新闻吗？')) {
      return;
    }

    try {
      await newsApi.approveNews(newsId);
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error('批准新闻失败:', error);
      alert('操作失败，请重试');
    }
  };

  const handleReject = (news: NewsWithDetails) => {
    setSelectedNews(news);
    setRejectionReason('');
    setShowRejectModal(true);
  };

  const confirmReject = async () => {
    if (!selectedNews) return;

    if (!rejectionReason.trim()) {
      alert('请输入拒绝理由');
      return;
    }

    try {
      await newsApi.rejectNews(selectedNews.id, rejectionReason);
      setShowRejectModal(false);
      setSelectedNews(null);
      setRejectionReason('');
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error('拒绝新闻失败:', error);
      alert('操作失败，请重试');
    }
  };

  const handlePreview = (news: NewsWithDetails) => {
    setSelectedNews(news);
  };

  if (!isAdmin()) {
    return (
      <div className="text-center py-12">
        <CheckIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">权限不足</h3>
        <p className="mt-1 text-sm text-gray-500">只有管理员可以审批新闻</p>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 头部 */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">新闻审批</h2>
        <p className="mt-1 text-sm text-gray-500">
          共有 {pendingNews.length} 条新闻待审批
        </p>
      </div>

      {/* 待审批新闻列表 */}
      <div className="bg-white shadow-md rounded-lg overflow-hidden">
        {pendingNews.length === 0 ? (
          <div className="text-center py-12">
            <CheckIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">暂无待审批新闻</h3>
            <p className="mt-1 text-sm text-gray-500">所有新闻都已处理完毕</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {pendingNews.map((news) => (
              <div key={news.id} className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {news.title}
                    </h3>
                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                      {news.excerpt}
                    </p>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>作者: {news.author.name}</span>
                      <span>分类: {news.category.name}</span>
                      <span>
                        提交时间: {format(new Date(news.created_at), 'yyyy-MM-dd HH:mm', { locale: zhCN })}
                      </span>
                    </div>
                    {news.tags && news.tags.length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {news.tags.map((tag, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                    )}
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <button
                      onClick={() => handlePreview(news)}
                      className="inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <EyeIcon className="h-4 w-4 mr-1" />
                      预览
                    </button>
                    <button
                      onClick={() => handleApprove(news.id)}
                      className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                    >
                      <CheckIcon className="h-4 w-4 mr-1" />
                      批准
                    </button>
                    <button
                      onClick={() => handleReject(news)}
                      className="inline-flex items-center px-3 py-1.5 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      <XMarkIcon className="h-4 w-4 mr-1" />
                      拒绝
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* 拒绝理由模态框 */}
      {showRejectModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                拒绝新闻发布
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                请输入拒绝理由，这将帮助作者了解需要改进的地方：
              </p>
              <textarea
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="请输入拒绝理由..."
              />
              <div className="flex justify-end space-x-3 mt-4">
                <button
                  onClick={() => setShowRejectModal(false)}
                  className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                >
                  取消
                </button>
                <button
                  onClick={confirmReject}
                  className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700"
                >
                  确认拒绝
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 新闻预览模态框 */}
      {selectedNews && !showRejectModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-10 mx-auto p-5 border max-w-4xl shadow-lg rounded-md bg-white">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-xl font-bold text-gray-900">
                {selectedNews.title}
              </h3>
              <button
                onClick={() => setSelectedNews(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>
            <div className="prose max-w-none">
              <div className="text-sm text-gray-600 mb-4">
                {selectedNews.excerpt}
              </div>
              <div 
                className="content"
                dangerouslySetInnerHTML={{ __html: selectedNews.content }}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
