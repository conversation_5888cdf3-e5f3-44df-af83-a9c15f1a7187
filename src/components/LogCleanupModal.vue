<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="handleBackdropClick">
    <div class="bg-white rounded-lg w-full max-w-md" @click.stop>
      <!-- 头部 -->
      <div class="flex justify-between items-center p-6 border-b border-gray-200">
        <h2 class="text-xl font-bold text-gray-900">清理日志</h2>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 内容 -->
      <div class="p-6">
        <div class="space-y-4">
          <!-- 警告提示 -->
          <div class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">警告</h3>
                <p class="mt-1 text-sm text-red-700">
                  清理日志操作不可撤销，请谨慎选择清理条件。
                </p>
              </div>
            </div>
          </div>

          <!-- 清理选项 -->
          <div>
            <h3 class="text-sm font-medium text-gray-900 mb-3">清理条件</h3>
            
            <!-- 按时间清理 -->
            <div class="space-y-3">
              <label class="flex items-center">
                <input
                  v-model="cleanupType"
                  type="radio"
                  value="time"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span class="ml-2 text-sm text-gray-700">按时间清理</span>
              </label>
              
              <div v-if="cleanupType === 'time'" class="ml-6 space-y-2">
                <label class="flex items-center">
                  <input
                    v-model="timeRange"
                    type="radio"
                    value="30"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <span class="ml-2 text-sm text-gray-600">删除30天前的日志</span>
                </label>
                <label class="flex items-center">
                  <input
                    v-model="timeRange"
                    type="radio"
                    value="90"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <span class="ml-2 text-sm text-gray-600">删除90天前的日志</span>
                </label>
                <label class="flex items-center">
                  <input
                    v-model="timeRange"
                    type="radio"
                    value="365"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <span class="ml-2 text-sm text-gray-600">删除1年前的日志</span>
                </label>
                <div class="flex items-center">
                  <input
                    v-model="timeRange"
                    type="radio"
                    value="custom"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  />
                  <span class="ml-2 text-sm text-gray-600">自定义天数:</span>
                  <input
                    v-model.number="customDays"
                    :disabled="timeRange !== 'custom'"
                    type="number"
                    min="1"
                    max="3650"
                    class="ml-2 w-20 px-2 py-1 border border-gray-300 rounded text-sm disabled:bg-gray-100"
                    placeholder="天数"
                  />
                </div>
              </div>
            </div>

            <!-- 按级别清理 -->
            <div class="space-y-3">
              <label class="flex items-center">
                <input
                  v-model="cleanupType"
                  type="radio"
                  value="level"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span class="ml-2 text-sm text-gray-700">按级别清理</span>
              </label>
              
              <div v-if="cleanupType === 'level'" class="ml-6 space-y-2">
                <label class="flex items-center">
                  <input
                    v-model="selectedLevels"
                    type="checkbox"
                    value="info"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="ml-2 text-sm text-gray-600">信息级别日志</span>
                </label>
                <label class="flex items-center">
                  <input
                    v-model="selectedLevels"
                    type="checkbox"
                    value="warning"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="ml-2 text-sm text-gray-600">警告级别日志</span>
                </label>
                <p class="text-xs text-gray-500 mt-2">
                  注意：错误和严重级别的日志不建议删除
                </p>
              </div>
            </div>

            <!-- 按操作类型清理 -->
            <div class="space-y-3">
              <label class="flex items-center">
                <input
                  v-model="cleanupType"
                  type="radio"
                  value="action"
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span class="ml-2 text-sm text-gray-700">按操作类型清理</span>
              </label>
              
              <div v-if="cleanupType === 'action'" class="ml-6 space-y-2">
                <label class="flex items-center">
                  <input
                    v-model="selectedActions"
                    type="checkbox"
                    value="login"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="ml-2 text-sm text-gray-600">登录日志</span>
                </label>
                <label class="flex items-center">
                  <input
                    v-model="selectedActions"
                    type="checkbox"
                    value="logout"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="ml-2 text-sm text-gray-600">登出日志</span>
                </label>
                <label class="flex items-center">
                  <input
                    v-model="selectedActions"
                    type="checkbox"
                    value="view"
                    class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <span class="ml-2 text-sm text-gray-600">查看日志</span>
                </label>
              </div>
            </div>

            <!-- 全部清理 -->
            <div class="space-y-3">
              <label class="flex items-center">
                <input
                  v-model="cleanupType"
                  type="radio"
                  value="all"
                  class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300"
                />
                <span class="ml-2 text-sm text-red-700 font-medium">清理所有日志</span>
              </label>
              
              <div v-if="cleanupType === 'all'" class="ml-6">
                <p class="text-xs text-red-600">
                  ⚠️ 这将删除所有日志记录，包括重要的安全和审计信息！
                </p>
              </div>
            </div>
          </div>

          <!-- 预估影响 -->
          <div v-if="cleanupType" class="bg-blue-50 border border-blue-200 rounded-md p-3">
            <h4 class="text-sm font-medium text-blue-800 mb-1">预估影响</h4>
            <p class="text-sm text-blue-700">
              {{ getEstimatedImpact() }}
            </p>
          </div>

          <!-- 确认选项 -->
          <div class="space-y-2">
            <label class="flex items-center">
              <input
                v-model="confirmCleanup"
                type="checkbox"
                class="h-4 w-4 text-red-600 focus:ring-red-500 border-gray-300 rounded"
              />
              <span class="ml-2 text-sm text-gray-700">
                我确认要执行此清理操作
              </span>
            </label>
            
            <label class="flex items-center">
              <input
                v-model="createBackup"
                type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span class="ml-2 text-sm text-gray-700">
                清理前创建备份
              </span>
            </label>
          </div>
        </div>
      </div>

      <!-- 底部操作 -->
      <div class="flex justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
        <button
          @click="$emit('close')"
          class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          取消
        </button>
        <button
          @click="handleConfirm"
          :disabled="!canConfirm"
          class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 disabled:opacity-50"
        >
          确认清理
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineEmits } from 'vue'

// Emits
const emit = defineEmits<{
  close: []
  confirm: [options: any]
}>()

// 响应式数据
const cleanupType = ref('')
const timeRange = ref('30')
const customDays = ref(30)
const selectedLevels = ref<string[]>([])
const selectedActions = ref<string[]>([])
const confirmCleanup = ref(false)
const createBackup = ref(true)

// 计算属性
const canConfirm = computed(() => {
  return cleanupType.value && confirmCleanup.value
})

// 方法
const handleBackdropClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    emit('close')
  }
}

const getEstimatedImpact = () => {
  switch (cleanupType.value) {
    case 'time':
      const days = timeRange.value === 'custom' ? customDays.value : parseInt(timeRange.value)
      return `将删除 ${days} 天前的所有日志记录，预计影响约 ${Math.floor(days / 7)} 周的历史数据。`
    
    case 'level':
      if (selectedLevels.value.length === 0) {
        return '请选择要清理的日志级别。'
      }
      return `将删除所有 ${selectedLevels.value.join('、')} 级别的日志记录。`
    
    case 'action':
      if (selectedActions.value.length === 0) {
        return '请选择要清理的操作类型。'
      }
      return `将删除所有 ${selectedActions.value.join('、')} 类型的日志记录。`
    
    case 'all':
      return '⚠️ 将删除系统中的所有日志记录！这是不可逆的操作。'
    
    default:
      return '请选择清理条件。'
  }
}

const handleConfirm = () => {
  const options: any = {
    type: cleanupType.value,
    createBackup: createBackup.value
  }

  switch (cleanupType.value) {
    case 'time':
      options.days = timeRange.value === 'custom' ? customDays.value : parseInt(timeRange.value)
      break
    
    case 'level':
      options.levels = selectedLevels.value
      break
    
    case 'action':
      options.actions = selectedActions.value
      break
  }

  emit('confirm', options)
}
</script>

<style scoped>
/* 单选按钮和复选框样式 */
input[type="radio"]:checked {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

input[type="checkbox"]:checked {
  background-color: #3b82f6;
  border-color: #3b82f6;
}

/* 禁用状态样式 */
input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
