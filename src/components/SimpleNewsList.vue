<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 头部导航 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <NewspaperIcon class="h-5 w-5 text-white" />
            </div>
            <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              荣联科技新闻中心
            </h1>
          </div>
          <div class="flex items-center space-x-3">
            <button
              @click="handleManualRefresh"
              :disabled="refreshing"
              class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              :class="{ 'animate-pulse': refreshing }"
              :title="refreshing ? '刷新中...' : '刷新新闻'"
            >
              <ArrowPathIcon :class="['h-4 w-4', { 'animate-spin': refreshing }]" />
            </button>
            <button
              @click="$emit('login')"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <ArrowRightOnRectangleIcon class="h-4 w-4 mr-2" />
              管理员登录
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 搜索和筛选 -->
      <div class="mb-8 bg-white rounded-lg shadow-sm p-6">
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="flex-1">
            <div class="relative">
              <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                v-model="searchQuery"
                type="text"
                placeholder="搜索新闻标题或内容..."
                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <div class="sm:w-48">
            <select
              v-model="selectedCategory"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">所有分类</option>
              <option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>
        </div>
      </div>

      <!-- 新闻列表 -->
      <div v-if="loading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>

      <div v-else-if="filteredNews.length === 0" class="text-center py-12 bg-white rounded-lg shadow-sm">
        <div class="text-gray-500">
          <NewspaperIcon class="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <h3 class="text-lg font-medium mb-2">暂无新闻</h3>
          <p>目前没有已发布的新闻内容</p>
          <p class="text-sm mt-2">管理员可以登录后台创建新闻</p>
        </div>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <article
          v-for="item in filteredNews"
          :key="item.id"
          class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden cursor-pointer news-card"
          @click="handleViewNews(item)"
        >
          <div class="p-6">
            <!-- 标题 -->
            <h2 class="text-lg font-semibold text-gray-900 mb-3 line-clamp-2 hover:text-blue-600 transition-colors">
              {{ item.title }}
            </h2>

            <!-- 摘要 -->
            <p class="text-gray-600 text-sm mb-4 line-clamp-3">
              {{ item.excerpt }}
            </p>

            <!-- 元信息 -->
            <div class="space-y-2 mb-4">
              <div class="flex items-center text-xs text-gray-500">
                <FolderIcon class="h-4 w-4 mr-1" />
                <span>{{ item.category.name }}</span>
              </div>
              <div class="flex items-center text-xs text-gray-500">
                <UserIcon class="h-4 w-4 mr-1" />
                <span>{{ item.author.name }}</span>
              </div>
              <div class="flex items-center text-xs text-gray-500">
                <CalendarIcon class="h-4 w-4 mr-1" />
                <span>{{ formatDate(item.published_at || item.created_at) }}</span>
              </div>
            </div>

            <!-- 标签 -->
            <div v-if="item.tags && item.tags.length > 0" class="mb-4">
              <div class="flex flex-wrap gap-1">
                <span
                  v-for="(tag, index) in item.tags.slice(0, 3)"
                  :key="index"
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {{ tag }}
                </span>
                <span v-if="item.tags.length > 3" class="text-xs text-gray-500">
                  +{{ item.tags.length - 3 }}
                </span>
              </div>
            </div>

            <!-- 底部信息 -->
            <div class="flex items-center justify-between pt-4 border-t border-gray-100">
              <div class="flex items-center text-xs text-gray-500">
                <EyeIcon class="h-4 w-4 mr-1" />
                <span>{{ item.view_count }} 次浏览</span>
              </div>
              <span class="text-xs text-blue-600 font-medium">
                阅读全文 →
              </span>
            </div>
          </div>
        </article>
      </div>

      <!-- 显示总数信息 -->
      <div class="text-center mt-8">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 inline-block">
          <p class="text-sm text-blue-800 font-medium">
            📊 新闻统计信息
          </p>
          <p class="text-sm text-blue-600 mt-1">
            当前显示 <span class="font-bold text-blue-800">{{ filteredNews.length }}</span> 条已发布的新闻
          </p>
          <p class="text-xs text-blue-500 mt-1">
            筛选条件: status = 'published', limit = {{ filters.limit }}
          </p>
        </div>
      </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-200 mt-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center text-gray-500 text-sm">
          <p>&copy; 2024 荣联科技新闻中心. 保留所有权利.</p>
        </div>
      </div>
    </footer>

    <!-- 新闻查看器 -->
    <NewsViewer
      v-if="showViewer && selectedNews"
      :news="selectedNews"
      @close="handleCloseViewer"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import {
  EyeIcon,
  CalendarIcon,
  UserIcon,
  FolderIcon,
  MagnifyingGlassIcon,
  ArrowRightOnRectangleIcon,
  ArrowPathIcon,
  NewspaperIcon
} from '@heroicons/vue/24/outline'
import { useNewsStore } from '@/stores/news'
import { categoryApi } from '@/lib/vue-api'
import NewsViewer from '@/components/NewsViewer.vue'
import type { NewsWithDetails, Category, NewsFilters } from '@/types/vue-types'

// Props & Emits
defineEmits<{
  login: []
}>()

// Store
const newsStore = useNewsStore()

// Reactive data
const categories = ref<Category[]>([])
const selectedNews = ref<NewsWithDetails | null>(null)
const showViewer = ref(false)
const refreshing = ref(false)
const searchQuery = ref('')
const selectedCategory = ref('')

// Computed
const loading = computed(() => newsStore.loading)
const filters = computed(() => newsStore.filters)

const filteredNews = computed(() => {
  let result = newsStore.publishedNews
  
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(item => 
      item.title.toLowerCase().includes(query) ||
      item.excerpt.toLowerCase().includes(query)
    )
  }
  
  if (selectedCategory.value) {
    result = result.filter(item => item.category_id === selectedCategory.value)
  }
  
  return result
})

// Methods
const formatDate = (dateString: string) => {
  return format(new Date(dateString), 'yyyy年MM月dd日', { locale: zhCN })
}

const handleViewNews = (newsItem: NewsWithDetails) => {
  selectedNews.value = newsItem
  showViewer.value = true
  // 增加浏览量
  newsStore.incrementViewCount(newsItem.id)
}

const handleCloseViewer = () => {
  showViewer.value = false
  selectedNews.value = null
}

const handleManualRefresh = async () => {
  refreshing.value = true
  try {
    await newsStore.fetchNews({ status: 'published', limit: 50 })
  } finally {
    refreshing.value = false
  }
}

const loadCategories = async () => {
  try {
    const data = await categoryApi.getCategories()
    categories.value = data
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

// Lifecycle
onMounted(async () => {
  await Promise.all([
    newsStore.fetchNews({ status: 'published', limit: 50 }),
    loadCategories()
  ])
})

// 监听搜索和分类变化
watch([searchQuery, selectedCategory], () => {
  // 这里可以添加防抖逻辑
}, { debounce: 300 })
</script>
