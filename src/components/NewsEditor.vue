<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="handleBackdropClick">
    <div class="bg-white rounded-lg w-full max-w-4xl max-h-[95vh] overflow-hidden" @click.stop>
      <!-- 头部 -->
      <div class="flex justify-between items-center p-6 border-b border-gray-200">
        <h2 class="text-xl font-bold text-gray-900">
          {{ isEdit ? '编辑新闻' : '创建新闻' }}
        </h2>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 表单内容 -->
      <div class="p-6 overflow-y-auto max-h-[calc(95vh-140px)]">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- 基本信息 -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- 标题 -->
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                标题 <span class="text-red-500">*</span>
              </label>
              <input
                v-model="form.title"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="请输入新闻标题"
              />
            </div>

            <!-- 分类 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                分类 <span class="text-red-500">*</span>
              </label>
              <select
                v-model="form.category_id"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">请选择分类</option>
                <option v-for="category in categories" :key="category.id" :value="category.id">
                  {{ category.name }}
                </option>
              </select>
            </div>

            <!-- 状态 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">
                状态 <span class="text-red-500">*</span>
              </label>
              <select
                v-model="form.status"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="draft">草稿</option>
                <option value="pending" v-if="authStore.isAdmin || authStore.isEditor">待审批</option>
                <option value="published" v-if="authStore.isAdmin">已发布</option>
              </select>
            </div>
          </div>

          <!-- 摘要 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              摘要 <span class="text-red-500">*</span>
            </label>
            <textarea
              v-model="form.excerpt"
              required
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入新闻摘要，建议100-200字"
            ></textarea>
            <p class="mt-1 text-sm text-gray-500">{{ form.excerpt.length }}/200 字符</p>
          </div>

          <!-- 标签 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">标签</label>
            <div class="flex flex-wrap gap-2 mb-2">
              <span
                v-for="(tag, index) in form.tags"
                :key="index"
                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
              >
                {{ tag }}
                <button
                  type="button"
                  @click="removeTag(index)"
                  class="ml-1 text-blue-600 hover:text-blue-800"
                >
                  <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </span>
            </div>
            <div class="flex">
              <input
                v-model="newTag"
                type="text"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="输入标签后按回车添加"
                @keyup.enter="addTag"
              />
              <button
                type="button"
                @click="addTag"
                class="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700"
              >
                添加
              </button>
            </div>
          </div>

          <!-- 内容编辑器 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              内容 <span class="text-red-500">*</span>
            </label>
            
            <!-- 工具栏 -->
            <div class="border border-gray-300 rounded-t-md bg-gray-50 p-2 flex flex-wrap gap-2">
              <button
                type="button"
                @click="formatText('bold')"
                class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
                title="粗体"
              >
                <strong>B</strong>
              </button>
              <button
                type="button"
                @click="formatText('italic')"
                class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
                title="斜体"
              >
                <em>I</em>
              </button>
              <button
                type="button"
                @click="formatText('underline')"
                class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
                title="下划线"
              >
                <u>U</u>
              </button>
              <div class="border-l border-gray-300 mx-2"></div>
              <button
                type="button"
                @click="formatText('insertUnorderedList')"
                class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
                title="无序列表"
              >
                • 列表
              </button>
              <button
                type="button"
                @click="formatText('insertOrderedList')"
                class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
                title="有序列表"
              >
                1. 列表
              </button>
              <div class="border-l border-gray-300 mx-2"></div>
              <button
                type="button"
                @click="insertLink"
                class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
                title="插入链接"
              >
                🔗 链接
              </button>
              <button
                type="button"
                @click="insertImage"
                class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
                title="插入图片"
              >
                🖼️ 图片
              </button>
            </div>

            <!-- 编辑器 -->
            <div
              ref="editor"
              contenteditable="true"
              class="w-full min-h-[300px] px-3 py-2 border border-gray-300 border-t-0 rounded-b-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              @input="updateContent"
              @paste="handlePaste"
              v-html="form.content"
            ></div>
            
            <!-- 编辑器提示 -->
            <div class="mt-2 flex justify-between text-sm text-gray-500">
              <span>支持富文本编辑，可以插入图片和链接</span>
              <span>{{ getContentLength() }} 字符</span>
            </div>
          </div>
        </form>
      </div>

      <!-- 底部操作 -->
      <div class="flex justify-between items-center p-6 border-t border-gray-200 bg-gray-50">
        <div class="text-sm text-gray-500">
          <span v-if="isEdit">最后修改: {{ formatDate(form.updated_at) }}</span>
          <span v-else>创建时间: {{ formatDate(new Date().toISOString()) }}</span>
        </div>
        <div class="flex space-x-3">
          <button
            type="button"
            @click="$emit('close')"
            class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            取消
          </button>
          <button
            type="button"
            @click="saveDraft"
            :disabled="loading"
            class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            {{ loading ? '保存中...' : '保存草稿' }}
          </button>
          <button
            type="button"
            @click="handleSubmit"
            :disabled="loading || !isFormValid"
            class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
          >
            {{ loading ? '提交中...' : (isEdit ? '更新' : '创建') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useAuthStore } from '@/stores/auth'

// Props
interface Props {
  news?: any
  isEdit?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isEdit: false
})

// Emits
const emit = defineEmits<{
  close: []
  save: [data: any]
}>()

// Composables
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const newTag = ref('')
const editor = ref<HTMLElement>()

// 分类数据
const categories = ref([
  { id: '1', name: '公司新闻' },
  { id: '2', name: '产品发布' },
  { id: '3', name: '技术分享' },
  { id: '4', name: '行业动态' }
])

// 表单数据
const form = reactive({
  title: '',
  content: '',
  excerpt: '',
  category_id: '',
  status: 'draft',
  tags: [] as string[],
  created_at: '',
  updated_at: ''
})

// 计算属性
const isFormValid = computed(() => {
  return form.title.trim() && 
         form.content.trim() && 
         form.excerpt.trim() && 
         form.category_id &&
         form.status
})

// 方法
const handleBackdropClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    emit('close')
  }
}

const updateContent = () => {
  if (editor.value) {
    form.content = editor.value.innerHTML
  }
}

const getContentLength = () => {
  if (editor.value) {
    return editor.value.textContent?.length || 0
  }
  return 0
}

const formatText = (command: string) => {
  document.execCommand(command, false)
  editor.value?.focus()
}

const insertLink = () => {
  const url = prompt('请输入链接地址:')
  if (url) {
    document.execCommand('createLink', false, url)
    editor.value?.focus()
  }
}

const insertImage = () => {
  const url = prompt('请输入图片地址:')
  if (url) {
    document.execCommand('insertImage', false, url)
    editor.value?.focus()
  }
}

const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const text = event.clipboardData?.getData('text/plain') || ''
  document.execCommand('insertText', false, text)
}

const addTag = () => {
  const tag = newTag.value.trim()
  if (tag && !form.tags.includes(tag)) {
    form.tags.push(tag)
    newTag.value = ''
  }
}

const removeTag = (index: number) => {
  form.tags.splice(index, 1)
}

const saveDraft = async () => {
  if (!form.title.trim()) {
    alert('请输入标题')
    return
  }
  
  loading.value = true
  try {
    const data = {
      ...form,
      status: 'draft',
      updated_at: new Date().toISOString()
    }
    emit('save', data)
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  if (!isFormValid.value) {
    alert('请填写所有必填字段')
    return
  }
  
  loading.value = true
  try {
    const data = {
      ...form,
      updated_at: new Date().toISOString()
    }
    emit('save', data)
  } finally {
    loading.value = false
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 初始化
const initializeForm = () => {
  if (props.isEdit && props.news) {
    Object.assign(form, {
      title: props.news.title || '',
      content: props.news.content || '',
      excerpt: props.news.excerpt || '',
      category_id: props.news.category_id || '',
      status: props.news.status || 'draft',
      tags: [...(props.news.tags || [])],
      created_at: props.news.created_at || '',
      updated_at: props.news.updated_at || ''
    })
  } else {
    // 新建时的默认值
    form.created_at = new Date().toISOString()
    form.updated_at = new Date().toISOString()
  }
}

// 生命周期
onMounted(async () => {
  initializeForm()
  await nextTick()
  if (editor.value && form.content) {
    editor.value.innerHTML = form.content
  }
})
</script>

<style scoped>
/* 编辑器样式 */
[contenteditable="true"]:focus {
  outline: none;
}

[contenteditable="true"] p {
  margin: 0.5em 0;
}

[contenteditable="true"] ul,
[contenteditable="true"] ol {
  margin: 0.5em 0;
  padding-left: 2em;
}

[contenteditable="true"] img {
  max-width: 100%;
  height: auto;
}

[contenteditable="true"] a {
  color: #3b82f6;
  text-decoration: underline;
}
</style>
