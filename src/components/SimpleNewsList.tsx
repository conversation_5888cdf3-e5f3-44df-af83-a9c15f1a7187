'use client';

import { useState, useEffect } from 'react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import {
  EyeIcon,
  CalendarIcon,
  UserIcon,
  FolderIcon,
  MagnifyingGlassIcon,
  ArrowRightOnRectangleIcon,
  ArrowPathIcon,
  NewspaperIcon
} from '@heroicons/react/24/outline';
import { newsApi, categoryApi } from '@/lib/api';
import { newsEvents } from '@/lib/eventBus';
import NewsViewer from './NewsViewer';
import type { NewsWithDetails, Category, NewsFilters } from '@/types';

interface SimpleNewsListProps {
  onLogin: () => void;
}

export default function SimpleNewsList({ onLogin }: SimpleNewsListProps) {
  const [news, setNews] = useState<NewsWithDetails[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedNews, setSelectedNews] = useState<NewsWithDetails | null>(null);
  const [showViewer, setShowViewer] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [filters, setFilters] = useState<NewsFilters>({
    status: 'published',
    page: 1,
    limit: 50,
  });

  useEffect(() => {
    loadData();
    loadCategories();
  }, [filters]);

  // 监听新闻数据变更事件
  useEffect(() => {
    const handleRefreshNeeded = () => {
      console.log('收到新闻数据变更事件，刷新首页数据...');
      loadData();
    };

    newsEvents.onRefreshNeeded(handleRefreshNeeded);
    return () => {
      newsEvents.offRefreshNeeded(handleRefreshNeeded);
    };
  }, []);

  // 定期刷新数据
  useEffect(() => {
    const interval = setInterval(() => {
      loadData();
    }, 60000); // 每分钟刷新一次

    return () => clearInterval(interval);
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const data = await newsApi.getNews(filters);
      console.log('首页加载已发布新闻:', data.length, '条');
      setNews(data);
    } catch (error) {
      console.error('加载新闻失败:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const loadCategories = async () => {
    try {
      const data = await categoryApi.getCategories();
      setCategories(data);
    } catch (error) {
      console.error('加载分类失败:', error);
    }
  };

  const handleFilterChange = (key: keyof NewsFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined,
      page: 1,
    }));
  };

  const handleViewNews = (newsItem: NewsWithDetails) => {
    setSelectedNews(newsItem);
    setShowViewer(true);
  };

  const handleCloseViewer = () => {
    setShowViewer(false);
    setSelectedNews(null);
    loadData(); // 刷新数据以更新浏览量
  };

  const handleManualRefresh = async () => {
    setRefreshing(true);
    await loadData();
  };

  if (showViewer && selectedNews) {
    return (
      <NewsViewer
        news={selectedNews}
        onClose={handleCloseViewer}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部导航 */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <NewspaperIcon className="h-5 w-5 text-white" />
              </div>
              <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                荣联科技新闻中心
              </h1>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleManualRefresh}
                disabled={refreshing}
                className={`inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed ${
                  refreshing ? 'animate-pulse' : ''
                }`}
                title={refreshing ? "刷新中..." : "刷新新闻"}
              >
                <ArrowPathIcon className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              </button>
              <button
                onClick={onLogin}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <ArrowRightOnRectangleIcon className="h-4 w-4 mr-2" />
                管理员登录
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 搜索和筛选 */}
        <div className="mb-8 bg-white rounded-lg shadow-sm p-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索新闻标题或内容..."
                  value={filters.search || ''}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
            <div className="sm:w-48">
              <select
                value={filters.category_id || ''}
                onChange={(e) => handleFilterChange('category_id', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="">所有分类</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* 新闻列表 */}
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : news.length === 0 ? (
          <div className="text-center py-12 bg-white rounded-lg shadow-sm">
            <div className="text-gray-500">
              <NewspaperIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium mb-2">暂无新闻</h3>
              <p>目前没有已发布的新闻内容</p>
              <p className="text-sm mt-2">管理员可以登录后台创建新闻</p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {news.map((item) => (
              <article
                key={item.id}
                className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden cursor-pointer"
                onClick={() => handleViewNews(item)}
              >
                <div className="p-6">
                  {/* 标题 */}
                  <h2 className="text-lg font-semibold text-gray-900 mb-3 line-clamp-2 hover:text-blue-600 transition-colors">
                    {item.title}
                  </h2>

                  {/* 摘要 */}
                  <p className="text-gray-600 text-sm mb-4 line-clamp-3">
                    {item.excerpt}
                  </p>

                  {/* 元信息 */}
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center text-xs text-gray-500">
                      <FolderIcon className="h-4 w-4 mr-1" />
                      <span>{item.category.name}</span>
                    </div>
                    <div className="flex items-center text-xs text-gray-500">
                      <UserIcon className="h-4 w-4 mr-1" />
                      <span>{item.author.name}</span>
                    </div>
                    <div className="flex items-center text-xs text-gray-500">
                      <CalendarIcon className="h-4 w-4 mr-1" />
                      <span>
                        {format(new Date(item.published_at || item.created_at), 'yyyy年MM月dd日', { locale: zhCN })}
                      </span>
                    </div>
                  </div>

                  {/* 标签 */}
                  {item.tags && item.tags.length > 0 && (
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-1">
                        {item.tags.slice(0, 3).map((tag, index) => (
                          <span
                            key={index}
                            className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                          >
                            {tag}
                          </span>
                        ))}
                        {item.tags.length > 3 && (
                          <span className="text-xs text-gray-500">
                            +{item.tags.length - 3}
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* 底部信息 */}
                  <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                    <div className="flex items-center text-xs text-gray-500">
                      <EyeIcon className="h-4 w-4 mr-1" />
                      <span>{item.view_count} 次浏览</span>
                    </div>
                    <span className="text-xs text-blue-600 font-medium">
                      阅读全文 →
                    </span>
                  </div>
                </div>
              </article>
            ))}
          </div>
        )}

        {/* 显示总数信息 */}
        {news.length > 0 && (
          <div className="text-center mt-8">
            <p className="text-sm text-gray-500">
              共显示 {news.length} 条已发布的新闻
            </p>
          </div>
        )}
      </main>

      {/* 页脚 */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-500 text-sm">
            <p>&copy; 2024 荣联科技新闻中心. 保留所有权利.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
