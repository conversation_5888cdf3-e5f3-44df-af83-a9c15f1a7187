<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="text-center">
        <div class="mx-auto h-16 w-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center mb-6">
          <ExclamationTriangleIcon class="h-10 w-10 text-white" />
        </div>
        <h1 class="text-6xl font-bold text-gray-900 mb-4">404</h1>
        <h2 class="text-2xl font-semibold text-gray-700 mb-4">页面不存在</h2>
        <p class="text-gray-600 mb-8">
          抱歉，您访问的页面不存在或已被移除。
        </p>
        
        <div class="space-y-4">
          <router-link
            to="/"
            class="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <HomeIcon class="h-5 w-5 mr-2" />
            返回首页
          </router-link>
          
          <div class="text-center">
            <button
              @click="goBack"
              class="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              返回上一页
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 建议链接 -->
    <div class="mt-12 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <h3 class="text-lg font-medium text-gray-900 mb-4">您可能想要：</h3>
        <ul class="space-y-3">
          <li>
            <router-link
              to="/"
              class="flex items-center text-blue-600 hover:text-blue-800"
            >
              <NewspaperIcon class="h-5 w-5 mr-2" />
              浏览最新新闻
            </router-link>
          </li>
          <li>
            <router-link
              to="/login"
              class="flex items-center text-blue-600 hover:text-blue-800"
            >
              <UserIcon class="h-5 w-5 mr-2" />
              管理员登录
            </router-link>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import {
  ExclamationTriangleIcon,
  HomeIcon,
  NewspaperIcon,
  UserIcon,
} from '@heroicons/vue/24/outline'

const router = useRouter()

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    router.push('/')
  }
}
</script>
