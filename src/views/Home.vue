<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 头部导航 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
              </svg>
            </div>
            <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              荣联科技新闻中心
            </h1>
          </div>
          <div class="flex items-center space-x-3">
            <button
              @click="refreshNews"
              :disabled="loading"
              class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <svg class="h-4 w-4" :class="{ 'animate-spin': loading }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
            <button
              @click="showLoginModal = true"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
              </svg>
              管理员登录
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 搜索和筛选 -->
      <div class="mb-8 bg-white rounded-lg shadow-sm p-6">
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="flex-1">
            <div class="relative">
              <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              <input
                v-model="searchQuery"
                type="text"
                placeholder="搜索新闻标题或内容..."
                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <div class="sm:w-48">
            <select
              v-model="selectedCategory"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">所有分类</option>
              <option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>
        </div>
      </div>

      <!-- 新闻列表 -->
      <div v-if="loading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>

      <div v-else-if="filteredNews.length === 0" class="text-center py-12 bg-white rounded-lg shadow-sm">
        <div class="text-gray-500">
          <svg class="h-12 w-12 mx-auto mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
          </svg>
          <h3 class="text-lg font-medium mb-2">暂无新闻</h3>
          <p>目前没有已发布的新闻内容</p>
          <button
            @click="loadMockData"
            class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            加载示例数据
          </button>
        </div>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <article
          v-for="item in filteredNews"
          :key="item.id"
          class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden cursor-pointer"
          @click="handleViewNews(item)"
        >
          <div class="p-6">
            <!-- 标题 -->
            <h2 class="text-lg font-semibold text-gray-900 mb-3 line-clamp-2 hover:text-blue-600 transition-colors">
              {{ item.title }}
            </h2>

            <!-- 摘要 -->
            <p class="text-gray-600 text-sm mb-4 line-clamp-3">
              {{ item.excerpt }}
            </p>

            <!-- 元信息 -->
            <div class="space-y-2 mb-4">
              <div class="flex items-center text-xs text-gray-500">
                <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                </svg>
                <span>{{ item.category?.name || '未分类' }}</span>
              </div>
              <div class="flex items-center text-xs text-gray-500">
                <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span>{{ item.author?.name || '未知作者' }}</span>
              </div>
              <div class="flex items-center text-xs text-gray-500">
                <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2-2v16a2 2 0 002 2z" />
                </svg>
                <span>{{ formatDate(item.created_at) }}</span>
              </div>
            </div>

            <!-- 底部信息 -->
            <div class="flex items-center justify-between pt-4 border-t border-gray-100">
              <div class="flex items-center text-xs text-gray-500">
                <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <span>{{ item.view_count || 0 }} 次浏览</span>
              </div>
              <span class="text-xs text-blue-600 font-medium">
                阅读全文 →
              </span>
            </div>
          </div>
        </article>
      </div>

      <!-- 显示总数信息 -->
      <div v-if="filteredNews.length > 0" class="text-center mt-8">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 inline-block">
          <p class="text-sm text-blue-800 font-medium">
            📊 新闻统计信息
          </p>
          <p class="text-sm text-blue-600 mt-1">
            当前显示 <span class="font-bold text-blue-800">{{ filteredNews.length }}</span> 条已发布的新闻
          </p>
        </div>
      </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-200 mt-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center text-gray-500 text-sm">
          <p>&copy; 2024 荣联科技新闻中心. 保留所有权利.</p>
        </div>
      </div>
    </footer>

    <!-- 登录模态框 -->
    <div v-if="showLoginModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="showLoginModal = false">
      <div class="bg-white rounded-lg p-6 w-96" @click.stop>
        <h3 class="text-lg font-semibold mb-4">管理员登录</h3>

        <!-- 错误信息 -->
        <div v-if="authStore.error" class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
          <p class="text-sm text-red-600">{{ authStore.error }}</p>
        </div>

        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
            <input
              v-model="loginForm.email"
              type="email"
              :disabled="authStore.loading"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              placeholder="<EMAIL>"
            />
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">密码</label>
            <input
              v-model="loginForm.password"
              type="password"
              :disabled="authStore.loading"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
              placeholder="admin123"
              @keyup.enter="handleLogin"
            />
          </div>

          <!-- 提示信息 -->
          <div class="text-xs text-gray-500 bg-gray-50 p-2 rounded">
            <p><strong>测试账号：</strong></p>
            <p>管理员: <EMAIL> / admin123</p>
            <p>编辑: <EMAIL> / editor123</p>
          </div>

          <div class="flex justify-end space-x-3">
            <button
              @click="showLoginModal = false"
              :disabled="authStore.loading"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 disabled:opacity-50"
            >
              取消
            </button>
            <button
              @click="handleLogin"
              :disabled="authStore.loading || !loginForm.email || !loginForm.password"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
            >
              <svg v-if="authStore.loading" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ authStore.loading ? '登录中...' : '登录' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 新闻查看器 -->
    <div v-if="showViewer && selectedNews" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4" @click="handleCloseViewer">
      <div class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto" @click.stop>
        <div class="p-6">
          <div class="flex justify-between items-start mb-4">
            <h2 class="text-2xl font-bold text-gray-900">{{ selectedNews.title }}</h2>
            <button
              @click="handleCloseViewer"
              class="text-gray-400 hover:text-gray-600"
            >
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          <div class="text-sm text-gray-500 mb-6">
            {{ selectedNews.category?.name }} | {{ selectedNews.author?.name }} | {{ formatDate(selectedNews.created_at) }}
          </div>
          <div class="prose max-w-none">
            <p class="text-gray-700 leading-relaxed">{{ selectedNews.excerpt }}</p>
            <div class="mt-4 p-4 bg-gray-50 rounded-lg">
              <p class="text-gray-600">这里是新闻的详细内容区域。在完整版本中，这里会显示新闻的完整内容。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Composables
const router = useRouter()
const authStore = useAuthStore()

// 响应式数据
const newsList = ref<any[]>([])
const categories = ref<any[]>([])
const selectedNews = ref<any>(null)
const showViewer = ref(false)
const showLoginModal = ref(false)
const searchQuery = ref('')
const selectedCategory = ref('')
const loading = ref(false)
const loginForm = ref({
  email: '',
  password: ''
})

// 计算属性
const filteredNews = computed(() => {
  let result = newsList.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(item =>
      item.title.toLowerCase().includes(query) ||
      item.excerpt.toLowerCase().includes(query)
    )
  }

  if (selectedCategory.value) {
    result = result.filter(item => item.category?.id === selectedCategory.value)
  }

  return result
})

// 方法
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

const handleViewNews = (newsItem: any) => {
  selectedNews.value = newsItem
  showViewer.value = true
  console.log('查看新闻:', newsItem.title)
}

const handleCloseViewer = () => {
  showViewer.value = false
  selectedNews.value = null
}

const refreshNews = () => {
  loading.value = true
  setTimeout(() => {
    loadMockData()
    loading.value = false
  }, 1000)
}

const handleLogin = async () => {
  try {
    await authStore.login(loginForm.value.email, loginForm.value.password)
    showLoginModal.value = false
    loginForm.value = { email: '', password: '' }

    // 跳转到管理页面
    router.push('/admin')
  } catch (error) {
    console.error('登录失败:', error)
    // 错误信息已经在authStore中处理了
  }
}

const loadMockData = () => {
  // 创建更多模拟数据
  const mockNews = [
    {
      id: '1',
      title: '荣联科技2024年度总结大会成功举办',
      excerpt: '公司全体员工齐聚一堂，回顾过去一年的成就，展望未来发展方向。大会总结了公司在技术创新、市场拓展、团队建设等方面取得的重要成果。',
      category: { id: '1', name: '公司新闻' },
      author: { name: '张三' },
      created_at: '2024-12-15T10:00:00Z',
      view_count: 156
    },
    {
      id: '2',
      title: '新产品发布：智能办公系统V2.0',
      excerpt: '全新的智能办公系统正式发布，为企业提供更高效的办公解决方案。系统集成了AI助手、智能排程、协作工具等多项先进功能。',
      category: { id: '2', name: '产品发布' },
      author: { name: '李四' },
      created_at: '2024-12-14T14:30:00Z',
      view_count: 89
    },
    {
      id: '3',
      title: '技术分享：Vue 3 最佳实践',
      excerpt: '深入探讨Vue 3的新特性和最佳实践，助力团队技术提升。本次分享涵盖了Composition API、响应式系统、性能优化等核心内容。',
      category: { id: '3', name: '技术分享' },
      author: { name: '王五' },
      created_at: '2024-12-13T16:45:00Z',
      view_count: 234
    },
    {
      id: '4',
      title: '荣联科技荣获"年度最佳创新企业"奖项',
      excerpt: '在2024年度科技创新大会上，荣联科技凭借在人工智能和云计算领域的突出贡献，荣获"年度最佳创新企业"殊荣。',
      category: { id: '1', name: '公司新闻' },
      author: { name: '赵六' },
      created_at: '2024-12-12T09:15:00Z',
      view_count: 312
    },
    {
      id: '5',
      title: '云计算服务升级：性能提升50%',
      excerpt: '经过团队不懈努力，我们的云计算服务完成重大升级，整体性能提升50%，为客户提供更稳定、更高效的云服务体验。',
      category: { id: '2', name: '产品发布' },
      author: { name: '孙七' },
      created_at: '2024-12-11T11:20:00Z',
      view_count: 178
    },
    {
      id: '6',
      title: '数据安全新规发布，企业需加强防护措施',
      excerpt: '随着新的数据安全法规正式实施，企业需要重新审视和加强数据保护措施，确保合规经营和用户隐私安全。',
      category: { id: '4', name: '行业动态' },
      author: { name: '周八' },
      created_at: '2024-12-10T13:30:00Z',
      view_count: 145
    }
  ]

  const mockCategories = [
    { id: '1', name: '公司新闻' },
    { id: '2', name: '产品发布' },
    { id: '3', name: '技术分享' },
    { id: '4', name: '行业动态' }
  ]

  newsList.value = mockNews
  categories.value = mockCategories
  console.log('✅ 模拟数据加载成功:', mockNews.length, '条新闻')
}

// 生命周期
onMounted(() => {
  console.log('✅ 荣联科技新闻中心已加载')
  // 自动加载模拟数据
  loadMockData()
})
</script>
