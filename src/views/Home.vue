<template>
  <div style="padding: 20px; background: white; margin: 20px; border: 2px solid green;">
    <h2 style="color: green; font-size: 20px;">✅ Home 组件已加载</h2>
    <p>这是 Vue 3 的 Home 页面组件</p>
    <p>计数器: {{ count }}</p>
    <button @click="increment" style="background: green; color: white; padding: 5px 10px; border: none; cursor: pointer;">
      点击 +1
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const count = ref(0)

const increment = () => {
  count.value++
  console.log('计数器:', count.value)
}

onMounted(() => {
  console.log('✅ Home 组件已挂载')
})
</script>
