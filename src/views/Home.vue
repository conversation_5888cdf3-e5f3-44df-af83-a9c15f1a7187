<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <h1 class="text-4xl font-bold text-blue-600 mb-6">🎉 Vue 3 应用运行成功！</h1>

    <!-- 基本信息 -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
      <h2 class="text-2xl font-semibold mb-4">荣联科技新闻中心</h2>
      <p class="text-gray-600 mb-4">Vue 3 + TypeScript + Vite + Tailwind CSS</p>

      <!-- 状态信息 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="bg-green-50 border border-green-200 rounded p-4">
          <h3 class="font-semibold text-green-800">✅ Vue 3</h3>
          <p class="text-sm text-green-600">Composition API</p>
        </div>
        <div class="bg-blue-50 border border-blue-200 rounded p-4">
          <h3 class="font-semibold text-blue-800">🔧 Vite</h3>
          <p class="text-sm text-blue-600">快速构建工具</p>
        </div>
        <div class="bg-purple-50 border border-purple-200 rounded p-4">
          <h3 class="font-semibold text-purple-800">🎨 Tailwind</h3>
          <p class="text-sm text-purple-600">实用优先的CSS</p>
        </div>
      </div>

      <!-- 认证状态 -->
      <div class="bg-yellow-50 border border-yellow-200 rounded p-4 mb-6">
        <h3 class="font-semibold text-yellow-800 mb-2">🔐 认证状态</h3>
        <p class="text-sm text-yellow-600">状态: {{ authStore.isAuthenticated ? '已登录' : '未登录' }}</p>
        <p class="text-sm text-yellow-600">加载: {{ authStore.loading ? '加载中' : '已完成' }}</p>
        <p class="text-sm text-yellow-600">用户: {{ authStore.user?.name || '无' }}</p>
      </div>

      <!-- 操作按钮 -->
      <div class="space-x-4">
        <button
          @click="showLoginModal = true"
          class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          🔑 管理员登录
        </button>
        <button
          @click="loadNews"
          class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors"
        >
          📰 加载新闻
        </button>
        <button
          @click="testApi"
          class="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors"
        >
          🧪 测试API
        </button>
      </div>
    </div>

    <!-- 新闻数据 -->
    <div v-if="newsData.length > 0" class="bg-white rounded-lg shadow p-6 mb-6">
      <h3 class="text-xl font-semibold mb-4">📰 新闻数据 ({{ newsData.length }} 条)</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div
          v-for="news in newsData.slice(0, 6)"
          :key="news.id"
          class="border border-gray-200 rounded p-3"
        >
          <h4 class="font-medium text-sm mb-2">{{ news.title }}</h4>
          <p class="text-xs text-gray-600 mb-2">{{ news.excerpt }}</p>
          <div class="flex justify-between text-xs text-gray-500">
            <span>{{ news.category.name }}</span>
            <span>{{ news.status }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- API测试结果 -->
    <div v-if="apiTestResult" class="bg-white rounded-lg shadow p-6 mb-6">
      <h3 class="text-xl font-semibold mb-4">🧪 API测试结果</h3>
      <pre class="bg-gray-100 p-4 rounded text-sm overflow-x-auto">{{ apiTestResult }}</pre>
    </div>

    <!-- 登录模态框 -->
    <LoginModal
      :is-open="showLoginModal"
      @close="showLoginModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useNewsStore } from '@/stores/news'
import { newsApi, categoryApi } from '@/lib/vue-api'
import LoginModal from '@/components/LoginModal.vue'
import type { NewsWithDetails } from '@/types/vue-types'

const router = useRouter()
const authStore = useAuthStore()
const newsStore = useNewsStore()
const showLoginModal = ref(false)
const newsData = ref<NewsWithDetails[]>([])
const apiTestResult = ref('')

// 加载新闻数据
const loadNews = async () => {
  try {
    console.log('开始加载新闻数据...')
    const data = await newsStore.fetchNews({ status: 'published', limit: 50 })
    newsData.value = data
    console.log('新闻数据加载成功:', data.length, '条')
  } catch (error) {
    console.error('加载新闻失败:', error)
    apiTestResult.value = `加载新闻失败: ${error}`
  }
}

// 测试API
const testApi = async () => {
  try {
    console.log('开始测试API...')

    // 测试新闻API
    const newsResult = await newsApi.getNews({ status: 'published', limit: 5 })
    console.log('新闻API测试成功:', newsResult.length, '条')

    // 测试分类API
    const categoryResult = await categoryApi.getCategories()
    console.log('分类API测试成功:', categoryResult.length, '个')

    apiTestResult.value = JSON.stringify({
      news: newsResult.length,
      categories: categoryResult.length,
      timestamp: new Date().toLocaleString()
    }, null, 2)
  } catch (error) {
    console.error('API测试失败:', error)
    apiTestResult.value = `API测试失败: ${error}`
  }
}

// 监听认证状态变化
watch(
  () => authStore.isAuthenticated,
  (isAuthenticated) => {
    if (isAuthenticated) {
      console.log('用户已登录，准备跳转到管理页面')
      // 延迟跳转，让用户看到状态变化
      setTimeout(() => {
        router.push('/admin')
      }, 1000)
    }
  },
  { immediate: true }
)

onMounted(async () => {
  console.log('Home组件已挂载')

  // 自动加载新闻数据
  await loadNews()

  // 如果用户已经登录，直接跳转
  if (authStore.isAuthenticated) {
    console.log('用户已登录，直接跳转')
    router.push('/admin')
  }
})
</script>
