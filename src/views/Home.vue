<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 头部导航 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <svg class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
              </svg>
            </div>
            <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              荣联科技新闻中心
            </h1>
          </div>
          <div class="flex items-center space-x-3">
            <button
              @click="refreshNews"
              :disabled="loading"
              class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              <svg class="h-4 w-4" :class="{ 'animate-spin': loading }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
            <button
              @click="showLoginModal = true"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
              </svg>
              管理员登录
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- 搜索和筛选 -->
      <div class="mb-8 bg-white rounded-lg shadow-sm p-6">
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="flex-1">
            <div class="relative">
              <svg class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              <input
                v-model="searchQuery"
                type="text"
                placeholder="搜索新闻标题或内容..."
                class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>
          <div class="sm:w-48">
            <select
              v-model="selectedCategory"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">所有分类</option>
              <option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>
        </div>
      </div>

      <!-- 新闻列表 -->
      <div v-if="loading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>

      <div v-else-if="filteredNews.length === 0" class="text-center py-12 bg-white rounded-lg shadow-sm">
        <div class="text-gray-500">
          <svg class="h-12 w-12 mx-auto mb-4 text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
          </svg>
          <h3 class="text-lg font-medium mb-2">暂无新闻</h3>
          <p>目前没有已发布的新闻内容</p>
        </div>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <article
          v-for="item in filteredNews"
          :key="item.id"
          class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden cursor-pointer"
          @click="handleViewNews(item)"
        >
          <div class="p-6">
            <!-- 标题 -->
            <h2 class="text-lg font-semibold text-gray-900 mb-3 line-clamp-2 hover:text-blue-600 transition-colors">
              {{ item.title }}
            </h2>

            <!-- 摘要 -->
            <p class="text-gray-600 text-sm mb-4 line-clamp-3">
              {{ item.excerpt }}
            </p>

            <!-- 元信息 -->
            <div class="space-y-2 mb-4">
              <div class="flex items-center text-xs text-gray-500">
                <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
                </svg>
                <span>{{ item.category.name }}</span>
              </div>
              <div class="flex items-center text-xs text-gray-500">
                <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                <span>{{ item.author.name }}</span>
              </div>
              <div class="flex items-center text-xs text-gray-500">
                <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2-2v16a2 2 0 002 2z" />
                </svg>
                <span>{{ formatDate(item.published_at || item.created_at) }}</span>
              </div>
            </div>

            <!-- 标签 -->
            <div v-if="item.tags && item.tags.length > 0" class="mb-4">
              <div class="flex flex-wrap gap-1">
                <span
                  v-for="(tag, index) in item.tags.slice(0, 3)"
                  :key="index"
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                >
                  {{ tag }}
                </span>
                <span v-if="item.tags.length > 3" class="text-xs text-gray-500">
                  +{{ item.tags.length - 3 }}
                </span>
              </div>
            </div>

            <!-- 底部信息 -->
            <div class="flex items-center justify-between pt-4 border-t border-gray-100">
              <div class="flex items-center text-xs text-gray-500">
                <svg class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                <span>{{ item.view_count }} 次浏览</span>
              </div>
              <span class="text-xs text-blue-600 font-medium">
                阅读全文 →
              </span>
            </div>
          </div>
        </article>
      </div>

      <!-- 显示总数信息 -->
      <div class="text-center mt-8">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 inline-block">
          <p class="text-sm text-blue-800 font-medium">
            📊 新闻统计信息
          </p>
          <p class="text-sm text-blue-600 mt-1">
            当前显示 <span class="font-bold text-blue-800">{{ filteredNews.length }}</span> 条已发布的新闻
          </p>
        </div>
      </div>
    </main>

    <!-- 页脚 -->
    <footer class="bg-white border-t border-gray-200 mt-16">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="text-center text-gray-500 text-sm">
          <p>&copy; 2024 荣联科技新闻中心. 保留所有权利.</p>
        </div>
      </div>
    </footer>

    <!-- 登录模态框 -->
    <LoginModal
      v-if="showLoginModal"
      :is-open="showLoginModal"
      @close="showLoginModal = false"
    />

    <!-- 新闻查看器 -->
    <NewsViewer
      v-if="showViewer && selectedNews"
      :news="selectedNews"
      @close="handleCloseViewer"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { useAuthStore } from '@/stores/auth'
import { useNewsStore } from '@/stores/news'
import { categoryApi } from '@/lib/vue-api'
import LoginModal from '@/components/LoginModal.vue'
import NewsViewer from '@/components/NewsViewer.vue'
import type { NewsWithDetails, Category } from '@/types/vue-types'

// Composables
const router = useRouter()
const authStore = useAuthStore()
const newsStore = useNewsStore()

// Reactive data
const categories = ref<Category[]>([])
const selectedNews = ref<NewsWithDetails | null>(null)
const showViewer = ref(false)
const showLoginModal = ref(false)
const searchQuery = ref('')
const selectedCategory = ref('')
const loading = ref(false)

// Computed
const filteredNews = computed(() => {
  let result = newsStore.publishedNews

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(item =>
      item.title.toLowerCase().includes(query) ||
      item.excerpt.toLowerCase().includes(query)
    )
  }

  if (selectedCategory.value) {
    result = result.filter(item => item.category_id === selectedCategory.value)
  }

  return result
})

// Methods
const formatDate = (dateString: string) => {
  return format(new Date(dateString), 'yyyy年MM月dd日', { locale: zhCN })
}

const handleViewNews = (newsItem: NewsWithDetails) => {
  selectedNews.value = newsItem
  showViewer.value = true
  // 增加浏览量
  newsStore.incrementViewCount(newsItem.id)
}

const handleCloseViewer = () => {
  showViewer.value = false
  selectedNews.value = null
}

const refreshNews = async () => {
  loading.value = true
  try {
    await newsStore.fetchNews({ status: 'published', limit: 50 })
    console.log('✅ 新闻数据刷新成功')
  } catch (error) {
    console.error('❌ 刷新新闻失败:', error)
  } finally {
    loading.value = false
  }
}

const loadCategories = async () => {
  try {
    const data = await categoryApi.getCategories()
    categories.value = data
    console.log('✅ 分类数据加载成功:', data.length, '个')
  } catch (error) {
    console.error('❌ 加载分类失败:', error)
  }
}

// 监听认证状态变化
watch(
  () => authStore.isAuthenticated,
  (isAuthenticated) => {
    if (isAuthenticated) {
      console.log('✅ 用户已登录，跳转到管理页面')
      router.push('/admin')
    }
  }
)

// Lifecycle
onMounted(async () => {
  console.log('✅ Home 组件已挂载')

  try {
    // 并行加载数据
    await Promise.all([
      newsStore.fetchNews({ status: 'published', limit: 50 }),
      loadCategories()
    ])

    console.log('✅ 首页数据加载完成')
    console.log('📰 已发布新闻数量:', newsStore.publishedNews.length)
  } catch (error) {
    console.error('❌ 首页数据加载失败:', error)
  }
})
</script>
