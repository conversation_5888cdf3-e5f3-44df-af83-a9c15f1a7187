<template>
  <div style="padding: 20px; background: lightcoral; margin: 10px; border: 2px solid red;">
    <h2 style="color: darkred;">🏠 Home.vue 组件已加载</h2>
    <p>这是首页组件，路由正常工作！</p>
    <div style="margin: 20px 0; padding: 15px; background: white; border: 1px solid gray; border-radius: 5px;">
      <h3>🎉 Vue 3 + Router 测试成功！</h3>
      <p>✅ Vue 3 应用正常运行</p>
      <p>✅ Vue Router 路由正常</p>
      <p>✅ 组件系统正常</p>
      <p>✅ TypeScript 编译正常</p>
    </div>
    <div style="margin-top: 20px;">
      <h3>📰 荣联科技新闻中心</h3>
      <p>Vue 3 版本的新闻发布系统</p>
      <button @click="testFunction" style="background: green; color: white; padding: 10px; border: none; cursor: pointer; margin: 10px;">
        测试功能
      </button>
      <p v-if="testMessage">{{ testMessage }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const testMessage = ref('')

const testFunction = () => {
  testMessage.value = '✅ Vue 3 功能测试成功！时间: ' + new Date().toLocaleString()
  console.log('✅ Home.vue 测试功能正常')
}

onMounted(() => {
  console.log('✅ Home.vue 组件已挂载')
})
</script>
