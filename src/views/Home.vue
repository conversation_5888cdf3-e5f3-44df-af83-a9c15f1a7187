<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 如果用户未登录，显示简化的新闻列表 -->
    <template v-if="!authStore.isAuthenticated">
      <SimpleNewsList @login="showLoginModal = true" />
      <LoginModal 
        :is-open="showLoginModal" 
        @close="showLoginModal = false" 
      />
    </template>

    <!-- 如果用户已登录，重定向到管理页面 -->
    <template v-else>
      <div class="flex items-center justify-center min-h-screen">
        <div class="text-center">
          <h2 class="text-2xl font-bold text-gray-900 mb-4">正在跳转到管理页面...</h2>
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import SimpleNewsList from '@/components/SimpleNewsList.vue'
import LoginModal from '@/components/LoginModal.vue'

const router = useRouter()
const authStore = useAuthStore()
const showLoginModal = ref(false)

// 监听认证状态变化
watch(
  () => authStore.isAuthenticated,
  (isAuthenticated) => {
    if (isAuthenticated) {
      // 如果用户已登录，跳转到管理页面
      router.push('/admin')
    }
  },
  { immediate: true }
)

onMounted(() => {
  // 如果用户已经登录，直接跳转
  if (authStore.isAuthenticated) {
    router.push('/admin')
  }
})
</script>
