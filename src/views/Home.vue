<template>
  <div style="padding: 20px; background: lightcoral; margin: 10px; border: 2px solid red;">
    <h2 style="color: darkred;">🏠 Home.vue 组件已加载</h2>
    <p>这是首页组件，路由正常工作！</p>
    <div style="margin: 20px 0; padding: 15px; background: white; border: 1px solid gray; border-radius: 5px;">
      <h3>🎉 Vue 3 + Router 测试成功！</h3>
      <p>✅ Vue 3 应用正常运行</p>
      <p>✅ Vue Router 路由正常</p>
      <p>✅ 组件系统正常</p>
      <p>✅ TypeScript 编译正常</p>
    </div>
    <div style="margin-top: 20px;">
      <h3>📰 荣联科技新闻中心</h3>
      <p>Vue 3 版本的新闻发布系统</p>
      <button @click="testFunction" style="background: green; color: white; padding: 10px; border: none; cursor: pointer; margin: 10px;">
        测试状态管理
      </button>
      <p v-if="testMessage">{{ testMessage }}</p>

      <!-- 新闻列表 -->
      <div v-if="newsStore.publishedNews.length > 0" style="margin-top: 20px; padding: 15px; background: #f0f8ff; border: 1px solid #4169e1; border-radius: 5px;">
        <h3>📰 新闻列表 ({{ newsStore.publishedNews.length }} 条)</h3>
        <div v-for="news in newsStore.publishedNews.slice(0, 3)" :key="news.id"
             style="margin: 10px 0; padding: 10px; background: white; border-left: 3px solid #4169e1;">
          <h4 style="margin: 0 0 5px 0; color: #333;">{{ news.title }}</h4>
          <p style="margin: 0; color: #666; font-size: 14px;">{{ news.excerpt }}</p>
          <small style="color: #999;">{{ news.category.name }} | {{ news.author.name }}</small>
        </div>
        <p v-if="newsStore.publishedNews.length > 3" style="text-align: center; color: #666; margin: 10px 0;">
          还有 {{ newsStore.publishedNews.length - 3 }} 条新闻...
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useNewsStore } from '@/stores/news'

const testMessage = ref('')
const newsStore = useNewsStore()

const testFunction = async () => {
  try {
    testMessage.value = '🔄 正在测试状态管理...'

    // 测试新闻数据加载
    await newsStore.fetchNews({ status: 'published', limit: 10 })

    testMessage.value = `✅ 状态管理测试成功！加载了 ${newsStore.publishedNews.length} 条新闻`
    console.log('✅ Pinia 状态管理正常工作')
    console.log('📰 新闻数据:', newsStore.publishedNews)
  } catch (error) {
    testMessage.value = `❌ 状态管理测试失败: ${error}`
    console.error('❌ 状态管理测试失败:', error)
  }
}

onMounted(() => {
  console.log('✅ Home.vue 组件已挂载')
  console.log('📊 新闻Store状态:', {
    loading: newsStore.loading,
    newsCount: newsStore.news.length,
    publishedCount: newsStore.publishedNews.length
  })
})
</script>
