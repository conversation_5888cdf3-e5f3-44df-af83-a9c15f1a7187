<template>
  <div style="padding: 20px; background: lightcoral; margin: 10px; border: 2px solid red;">
    <h2 style="color: darkred;">🏠 Home.vue 组件已加载</h2>
    <p>这是首页组件，路由正常工作！</p>
    <div style="margin: 20px 0; padding: 15px; background: white; border: 1px solid gray; border-radius: 5px;">
      <h3>🎉 Vue 3 + Router 测试成功！</h3>
      <p>✅ Vue 3 应用正常运行</p>
      <p>✅ Vue Router 路由正常</p>
      <p>✅ 组件系统正常</p>
      <p>✅ TypeScript 编译正常</p>
    </div>
    <div style="margin-top: 20px;">
      <h3>📰 荣联科技新闻中心</h3>
      <p>Vue 3 版本的新闻发布系统</p>
      <button @click="testFunction" style="background: green; color: white; padding: 10px; border: none; cursor: pointer; margin: 10px;">
        测试功能
      </button>
      <button @click="testApi" style="background: blue; color: white; padding: 10px; border: none; cursor: pointer; margin: 10px;">
        测试API
      </button>
      <p v-if="testMessage">{{ testMessage }}</p>

      <!-- 简单的新闻列表 -->
      <div v-if="newsList.length > 0" style="margin-top: 20px; padding: 15px; background: #f0f8ff; border: 1px solid #4169e1; border-radius: 5px;">
        <h3>📰 新闻列表 ({{ newsList.length }} 条)</h3>
        <div v-for="news in newsList.slice(0, 3)" :key="news.id"
             style="margin: 10px 0; padding: 10px; background: white; border-left: 3px solid #4169e1;">
          <h4 style="margin: 0 0 5px 0; color: #333;">{{ news.title }}</h4>
          <p style="margin: 0; color: #666; font-size: 14px;">{{ news.excerpt }}</p>
          <small style="color: #999;">{{ news.category?.name || '未分类' }} | {{ news.author?.name || '未知作者' }}</small>
        </div>
        <p v-if="newsList.length > 3" style="text-align: center; color: #666; margin: 10px 0;">
          还有 {{ newsList.length - 3 }} 条新闻...
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const testMessage = ref('')
const newsList = ref<any[]>([])

const testFunction = () => {
  testMessage.value = '✅ Vue 3 功能测试成功！时间: ' + new Date().toLocaleString()
  console.log('✅ Home.vue 测试功能正常')
}

const testApi = () => {
  // 创建一些模拟数据来测试
  const mockNews = [
    {
      id: '1',
      title: '荣联科技2024年度总结大会成功举办',
      excerpt: '公司全体员工齐聚一堂，回顾过去一年的成就，展望未来发展方向。',
      category: { name: '公司新闻' },
      author: { name: '张三' }
    },
    {
      id: '2',
      title: '新产品发布：智能办公系统V2.0',
      excerpt: '全新的智能办公系统正式发布，为企业提供更高效的办公解决方案。',
      category: { name: '产品发布' },
      author: { name: '李四' }
    },
    {
      id: '3',
      title: '技术分享：Vue 3 最佳实践',
      excerpt: '深入探讨Vue 3的新特性和最佳实践，助力团队技术提升。',
      category: { name: '技术分享' },
      author: { name: '王五' }
    }
  ]

  newsList.value = mockNews
  testMessage.value = `✅ 模拟API测试成功！加载了 ${mockNews.length} 条新闻`
  console.log('✅ 模拟API测试成功')
}

onMounted(() => {
  console.log('✅ Home.vue 组件已挂载')
})
</script>
