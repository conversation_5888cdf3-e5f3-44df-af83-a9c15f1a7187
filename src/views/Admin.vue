<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航栏 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center space-x-4">
            <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <NewspaperIcon class="h-5 w-5 text-white" />
            </div>
            <h1 class="text-xl font-bold text-gray-900">新闻发布系统</h1>
          </div>

          <!-- 用户信息和登出 -->
          <div class="flex items-center space-x-4">
            <div class="text-sm text-gray-600">
              欢迎, <span class="font-medium">{{ authStore.user?.name }}</span>
              <span class="ml-2 px-2 py-1 text-xs rounded-full" 
                    :class="authStore.isAdmin ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'">
                {{ authStore.isAdmin ? '管理员' : '编辑' }}
              </span>
            </div>
            <button
              @click="handleLogout"
              class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <ArrowRightOnRectangleIcon class="h-4 w-4 mr-2" />
              登出
            </button>
          </div>
        </div>
      </div>
    </header>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="flex">
        <!-- 侧边导航 -->
        <nav class="w-64 bg-white rounded-lg shadow-sm p-6 mr-8">
          <ul class="space-y-2">
            <li>
              <router-link
                to="/admin/news"
                class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors"
                :class="isActiveRoute('/admin/news') ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'"
              >
                <NewspaperIcon class="h-5 w-5 mr-3" />
                新闻管理
              </router-link>
            </li>
            <li v-if="authStore.isAdmin">
              <router-link
                to="/admin/approval"
                class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors"
                :class="isActiveRoute('/admin/approval') ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'"
              >
                <CheckCircleIcon class="h-5 w-5 mr-3" />
                新闻审批
                <span v-if="pendingCount > 0" class="ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1">
                  {{ pendingCount }}
                </span>
              </router-link>
            </li>
            <li v-if="authStore.isAdmin">
              <router-link
                to="/admin/users"
                class="flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors"
                :class="isActiveRoute('/admin/users') ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'"
              >
                <UserGroupIcon class="h-5 w-5 mr-3" />
                用户管理
              </router-link>
            </li>
          </ul>
        </nav>

        <!-- 主要内容区域 -->
        <main class="flex-1">
          <router-view />
        </main>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  NewspaperIcon,
  CheckCircleIcon,
  UserGroupIcon,
  ArrowRightOnRectangleIcon,
} from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'
import { useNewsStore } from '@/stores/news'

// Composables
const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const newsStore = useNewsStore()

// Computed
const pendingCount = computed(() => newsStore.pendingNews.length)

// Methods
const isActiveRoute = (path: string) => {
  return route.path.startsWith(path)
}

const handleLogout = async () => {
  try {
    await authStore.logout()
    router.push('/')
  } catch (error) {
    console.error('登出失败:', error)
  }
}

// Lifecycle
onMounted(async () => {
  // 加载新闻数据以获取待审批数量
  if (authStore.isAdmin) {
    await newsStore.fetchNews({ status: 'pending' })
  }
})
</script>
