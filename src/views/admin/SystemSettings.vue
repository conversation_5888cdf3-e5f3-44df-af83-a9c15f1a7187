<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">系统设置</h1>
        <p class="text-gray-600 mt-1">管理系统配置和参数</p>
      </div>

      <div class="flex space-x-3">
        <button
          @click="resetToDefaults"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          恢复默认
        </button>
        <button
          @click="saveSettings"
          :disabled="loading || !hasChanges"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
        >
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          {{ loading ? '保存中...' : '保存设置' }}
        </button>
      </div>
    </div>

    <!-- 设置导航 -->
    <div class="bg-white rounded-lg shadow-sm">
      <div class="border-b border-gray-200">
        <nav class="flex space-x-8 px-6" aria-label="Tabs">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            class="py-4 px-1 border-b-2 font-medium text-sm"
            :class="activeTab === tab.id
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          >
            <div class="flex items-center">
              <component :is="tab.icon" class="h-5 w-5 mr-2" />
              {{ tab.name }}
            </div>
          </button>
        </nav>
      </div>

      <!-- 设置内容 -->
      <div class="p-6">
        <!-- 基本设置 -->
        <div v-if="activeTab === 'basic'" class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">基本信息</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- 网站名称 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">网站名称</label>
                <input
                  v-model="settings.basic.siteName"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="荣联科技新闻中心"
                />
              </div>

              <!-- 网站描述 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">网站描述</label>
                <input
                  v-model="settings.basic.siteDescription"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="企业新闻发布平台"
                />
              </div>

              <!-- 网站关键词 -->
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-700 mb-2">网站关键词</label>
                <input
                  v-model="settings.basic.siteKeywords"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="新闻,企业,科技,荣联"
                />
                <p class="mt-1 text-sm text-gray-500">多个关键词用逗号分隔</p>
              </div>

              <!-- 联系邮箱 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">联系邮箱</label>
                <input
                  v-model="settings.basic.contactEmail"
                  type="email"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>

              <!-- 版权信息 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">版权信息</label>
                <input
                  v-model="settings.basic.copyright"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="© 2024 荣联科技. All rights reserved."
                />
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">显示设置</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- 每页显示数量 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">每页显示新闻数</label>
                <select
                  v-model="settings.basic.pageSize"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="5">5篇</option>
                  <option value="10">10篇</option>
                  <option value="15">15篇</option>
                  <option value="20">20篇</option>
                </select>
              </div>

              <!-- 时区设置 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">时区</label>
                <select
                  v-model="settings.basic.timezone"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="Asia/Shanghai">北京时间 (UTC+8)</option>
                  <option value="UTC">协调世界时 (UTC)</option>
                  <option value="America/New_York">纽约时间 (UTC-5)</option>
                </select>
              </div>

              <!-- 语言设置 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">默认语言</label>
                <select
                  v-model="settings.basic.language"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="zh-CN">简体中文</option>
                  <option value="zh-TW">繁体中文</option>
                  <option value="en-US">English</option>
                </select>
              </div>

              <!-- 主题设置 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">主题模式</label>
                <select
                  v-model="settings.basic.theme"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="light">浅色主题</option>
                  <option value="dark">深色主题</option>
                  <option value="auto">跟随系统</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- 新闻设置 -->
        <div v-if="activeTab === 'news'" class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">发布设置</h3>
            <div class="space-y-4">
              <!-- 审批流程 -->
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900">启用审批流程</h4>
                  <p class="text-sm text-gray-500">新闻发布前需要管理员审批</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input
                    v-model="settings.news.requireApproval"
                    type="checkbox"
                    class="sr-only peer"
                  />
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <!-- 自动发布 -->
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900">允许自动发布</h4>
                  <p class="text-sm text-gray-500">管理员可以直接发布新闻</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input
                    v-model="settings.news.allowAutoPublish"
                    type="checkbox"
                    class="sr-only peer"
                  />
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <!-- 评论功能 -->
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900">启用评论功能</h4>
                  <p class="text-sm text-gray-500">允许用户对新闻进行评论</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input
                    v-model="settings.news.enableComments"
                    type="checkbox"
                    class="sr-only peer"
                  />
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">内容限制</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- 标题长度限制 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">标题最大长度</label>
                <input
                  v-model.number="settings.news.maxTitleLength"
                  type="number"
                  min="10"
                  max="200"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p class="mt-1 text-sm text-gray-500">建议10-100字符</p>
              </div>

              <!-- 摘要长度限制 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">摘要最大长度</label>
                <input
                  v-model.number="settings.news.maxExcerptLength"
                  type="number"
                  min="50"
                  max="500"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p class="mt-1 text-sm text-gray-500">建议100-300字符</p>
              </div>

              <!-- 内容长度限制 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">内容最大长度</label>
                <input
                  v-model.number="settings.news.maxContentLength"
                  type="number"
                  min="100"
                  max="50000"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p class="mt-1 text-sm text-gray-500">建议1000-10000字符</p>
              </div>

              <!-- 标签数量限制 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">最大标签数量</label>
                <input
                  v-model.number="settings.news.maxTags"
                  type="number"
                  min="1"
                  max="20"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p class="mt-1 text-sm text-gray-500">每篇新闻最多可添加的标签数</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户设置 -->
        <div v-if="activeTab === 'users'" class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">注册设置</h3>
            <div class="space-y-4">
              <!-- 允许注册 -->
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900">允许用户注册</h4>
                  <p class="text-sm text-gray-500">开放用户自主注册功能</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input
                    v-model="settings.users.allowRegistration"
                    type="checkbox"
                    class="sr-only peer"
                  />
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <!-- 邮箱验证 -->
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900">邮箱验证</h4>
                  <p class="text-sm text-gray-500">注册时需要验证邮箱地址</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input
                    v-model="settings.users.requireEmailVerification"
                    type="checkbox"
                    class="sr-only peer"
                  />
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">密码策略</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- 最小密码长度 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">最小密码长度</label>
                <input
                  v-model.number="settings.users.minPasswordLength"
                  type="number"
                  min="6"
                  max="32"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <!-- 密码复杂度 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">密码复杂度要求</label>
                <select
                  v-model="settings.users.passwordComplexity"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="low">低（仅字母数字）</option>
                  <option value="medium">中（包含特殊字符）</option>
                  <option value="high">高（大小写+数字+特殊字符）</option>
                </select>
              </div>

              <!-- 会话超时 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">会话超时时间（小时）</label>
                <input
                  v-model.number="settings.users.sessionTimeout"
                  type="number"
                  min="1"
                  max="168"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <!-- 默认角色 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">新用户默认角色</label>
                <select
                  v-model="settings.users.defaultRole"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="viewer">查看者</option>
                  <option value="editor">编辑</option>
                </select>
              </div>
            </div>
          </div>
        </div>

        <!-- 安全设置 -->
        <div v-if="activeTab === 'security'" class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">访问控制</h3>
            <div class="space-y-4">
              <!-- IP白名单 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">IP白名单</label>
                <textarea
                  v-model="settings.security.ipWhitelist"
                  rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="***********&#10;10.0.0.0/24&#10;留空表示允许所有IP访问"
                ></textarea>
                <p class="mt-1 text-sm text-gray-500">每行一个IP地址或CIDR网段，留空允许所有IP</p>
              </div>

              <!-- 登录限制 -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">最大登录尝试次数</label>
                  <input
                    v-model.number="settings.security.maxLoginAttempts"
                    type="number"
                    min="3"
                    max="10"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">锁定时间（分钟）</label>
                  <input
                    v-model.number="settings.security.lockoutDuration"
                    type="number"
                    min="5"
                    max="1440"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">安全选项</h3>
            <div class="space-y-4">
              <!-- 强制HTTPS -->
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900">强制HTTPS</h4>
                  <p class="text-sm text-gray-500">自动重定向HTTP请求到HTTPS</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input
                    v-model="settings.security.forceHttps"
                    type="checkbox"
                    class="sr-only peer"
                  />
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <!-- 启用日志记录 -->
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900">启用操作日志</h4>
                  <p class="text-sm text-gray-500">记录用户的重要操作</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input
                    v-model="settings.security.enableLogging"
                    type="checkbox"
                    class="sr-only peer"
                  />
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <!-- 双因子认证 -->
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900">启用双因子认证</h4>
                  <p class="text-sm text-gray-500">为管理员账户启用2FA</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input
                    v-model="settings.security.enableTwoFactor"
                    type="checkbox"
                    class="sr-only peer"
                  />
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- 邮件设置 -->
        <div v-if="activeTab === 'email'" class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">SMTP配置</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <!-- SMTP服务器 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">SMTP服务器</label>
                <input
                  v-model="settings.email.smtpHost"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="smtp.gmail.com"
                />
              </div>

              <!-- SMTP端口 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">SMTP端口</label>
                <input
                  v-model.number="settings.email.smtpPort"
                  type="number"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="587"
                />
              </div>

              <!-- 用户名 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
                <input
                  v-model="settings.email.smtpUsername"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>

              <!-- 密码 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">密码</label>
                <input
                  v-model="settings.email.smtpPassword"
                  type="password"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="••••••••"
                />
              </div>

              <!-- 发件人名称 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">发件人名称</label>
                <input
                  v-model="settings.email.fromName"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="荣联科技新闻中心"
                />
              </div>

              <!-- 发件人邮箱 -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">发件人邮箱</label>
                <input
                  v-model="settings.email.fromEmail"
                  type="email"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">邮件选项</h3>
            <div class="space-y-4">
              <!-- 启用SSL -->
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900">启用SSL/TLS</h4>
                  <p class="text-sm text-gray-500">使用加密连接发送邮件</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input
                    v-model="settings.email.enableSsl"
                    type="checkbox"
                    class="sr-only peer"
                  />
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <!-- 测试邮件 -->
              <div class="border border-gray-200 rounded-lg p-4">
                <h4 class="text-sm font-medium text-gray-900 mb-2">测试邮件配置</h4>
                <div class="flex space-x-3">
                  <input
                    v-model="testEmail"
                    type="email"
                    class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="输入测试邮箱地址"
                  />
                  <button
                    @click="sendTestEmail"
                    :disabled="!testEmail || emailTesting"
                    class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                  >
                    {{ emailTesting ? '发送中...' : '发送测试邮件' }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 备份设置 -->
        <div v-if="activeTab === 'backup'" class="space-y-6">
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">自动备份</h3>
            <div class="space-y-4">
              <!-- 启用自动备份 -->
              <div class="flex items-center justify-between">
                <div>
                  <h4 class="text-sm font-medium text-gray-900">启用自动备份</h4>
                  <p class="text-sm text-gray-500">定期自动备份系统数据</p>
                </div>
                <label class="relative inline-flex items-center cursor-pointer">
                  <input
                    v-model="settings.backup.enableAutoBackup"
                    type="checkbox"
                    class="sr-only peer"
                  />
                  <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>

              <!-- 备份频率 -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">备份频率</label>
                  <select
                    v-model="settings.backup.frequency"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="daily">每日</option>
                    <option value="weekly">每周</option>
                    <option value="monthly">每月</option>
                  </select>
                </div>

                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">保留备份数量</label>
                  <input
                    v-model.number="settings.backup.retentionCount"
                    type="number"
                    min="1"
                    max="30"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">手动操作</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                @click="createBackup"
                :disabled="backupLoading"
                class="flex items-center justify-center px-4 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
              >
                <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                </svg>
                {{ backupLoading ? '备份中...' : '立即备份' }}
              </button>

              <button
                @click="showRestoreModal = true"
                class="flex items-center justify-center px-4 py-3 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                恢复备份
              </button>
            </div>
          </div>

          <!-- 备份历史 -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">备份历史</h3>
            <div class="bg-gray-50 rounded-lg p-4">
              <div class="space-y-3">
                <div
                  v-for="backup in backupHistory"
                  :key="backup.id"
                  class="flex items-center justify-between p-3 bg-white rounded border"
                >
                  <div>
                    <p class="text-sm font-medium text-gray-900">{{ backup.name }}</p>
                    <p class="text-xs text-gray-500">{{ formatDate(backup.created_at) }} • {{ backup.size }}</p>
                  </div>
                  <div class="flex space-x-2">
                    <button
                      @click="downloadBackup(backup)"
                      class="text-blue-600 hover:text-blue-800 text-sm"
                    >
                      下载
                    </button>
                    <button
                      @click="deleteBackup(backup)"
                      class="text-red-600 hover:text-red-800 text-sm"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 恢复备份模态框 -->
    <div v-if="showRestoreModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-6 w-full max-w-md">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">恢复备份</h3>
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">选择备份文件</label>
            <input
              type="file"
              accept=".json,.sql,.zip"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          <div class="bg-yellow-50 border border-yellow-200 rounded-md p-3">
            <div class="flex">
              <svg class="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
              <div class="ml-3">
                <p class="text-sm text-yellow-800">
                  <strong>警告：</strong>恢复备份将覆盖当前所有数据，此操作不可撤销。
                </p>
              </div>
            </div>
          </div>
        </div>
        <div class="flex justify-end space-x-3 mt-6">
          <button
            @click="showRestoreModal = false"
            class="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            取消
          </button>
          <button
            class="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
          >
            确认恢复
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'

// Composables
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const emailTesting = ref(false)
const backupLoading = ref(false)
const activeTab = ref('basic')
const testEmail = ref('')
const showRestoreModal = ref(false)

// 选项卡配置
const tabs = [
  {
    id: 'basic',
    name: '基本设置',
    icon: 'svg'
  },
  {
    id: 'news',
    name: '新闻设置',
    icon: 'svg'
  },
  {
    id: 'users',
    name: '用户设置',
    icon: 'svg'
  },
  {
    id: 'security',
    name: '安全设置',
    icon: 'svg'
  },
  {
    id: 'email',
    name: '邮件设置',
    icon: 'svg'
  },
  {
    id: 'backup',
    name: '备份设置',
    icon: 'svg'
  }
]

// 系统设置数据
const settings = reactive({
  basic: {
    siteName: '荣联科技新闻中心',
    siteDescription: '企业新闻发布平台',
    siteKeywords: '新闻,企业,科技,荣联',
    contactEmail: '<EMAIL>',
    copyright: '© 2024 荣联科技. All rights reserved.',
    pageSize: 10,
    timezone: 'Asia/Shanghai',
    language: 'zh-CN',
    theme: 'light'
  },
  news: {
    requireApproval: true,
    allowAutoPublish: true,
    enableComments: false,
    maxTitleLength: 100,
    maxExcerptLength: 200,
    maxContentLength: 10000,
    maxTags: 10
  },
  users: {
    allowRegistration: false,
    requireEmailVerification: true,
    minPasswordLength: 8,
    passwordComplexity: 'medium',
    sessionTimeout: 24,
    defaultRole: 'viewer'
  },
  security: {
    ipWhitelist: '',
    maxLoginAttempts: 5,
    lockoutDuration: 30,
    forceHttps: true,
    enableLogging: true,
    enableTwoFactor: false
  },
  email: {
    smtpHost: '',
    smtpPort: 587,
    smtpUsername: '',
    smtpPassword: '',
    fromName: '荣联科技新闻中心',
    fromEmail: '<EMAIL>',
    enableSsl: true
  },
  backup: {
    enableAutoBackup: true,
    frequency: 'daily',
    retentionCount: 7
  }
})

// 原始设置（用于检测变更）
const originalSettings = ref({})

// 备份历史
const backupHistory = ref([
  {
    id: '1',
    name: 'backup_2024-12-15_10-30.zip',
    size: '2.5 MB',
    created_at: '2024-12-15T10:30:00Z'
  },
  {
    id: '2',
    name: 'backup_2024-12-14_10-30.zip',
    size: '2.3 MB',
    created_at: '2024-12-14T10:30:00Z'
  },
  {
    id: '3',
    name: 'backup_2024-12-13_10-30.zip',
    size: '2.1 MB',
    created_at: '2024-12-13T10:30:00Z'
  }
])

// 计算属性
const hasChanges = computed(() => {
  return JSON.stringify(settings) !== JSON.stringify(originalSettings.value)
})

// 方法
const saveSettings = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 更新原始设置
    originalSettings.value = JSON.parse(JSON.stringify(settings))

    console.log('✅ 设置保存成功')
    alert('设置保存成功！')
  } catch (error) {
    console.error('❌ 保存设置失败:', error)
    alert('保存失败: ' + (error as Error).message)
  } finally {
    loading.value = false
  }
}

const resetToDefaults = () => {
  if (confirm('确定要恢复所有设置到默认值吗？此操作不可撤销。')) {
    // 恢复默认设置
    Object.assign(settings.basic, {
      siteName: '荣联科技新闻中心',
      siteDescription: '企业新闻发布平台',
      siteKeywords: '新闻,企业,科技,荣联',
      contactEmail: '<EMAIL>',
      copyright: '© 2024 荣联科技. All rights reserved.',
      pageSize: 10,
      timezone: 'Asia/Shanghai',
      language: 'zh-CN',
      theme: 'light'
    })

    Object.assign(settings.news, {
      requireApproval: true,
      allowAutoPublish: true,
      enableComments: false,
      maxTitleLength: 100,
      maxExcerptLength: 200,
      maxContentLength: 10000,
      maxTags: 10
    })

    Object.assign(settings.users, {
      allowRegistration: false,
      requireEmailVerification: true,
      minPasswordLength: 8,
      passwordComplexity: 'medium',
      sessionTimeout: 24,
      defaultRole: 'viewer'
    })

    Object.assign(settings.security, {
      ipWhitelist: '',
      maxLoginAttempts: 5,
      lockoutDuration: 30,
      forceHttps: true,
      enableLogging: true,
      enableTwoFactor: false
    })

    Object.assign(settings.email, {
      smtpHost: '',
      smtpPort: 587,
      smtpUsername: '',
      smtpPassword: '',
      fromName: '荣联科技新闻中心',
      fromEmail: '<EMAIL>',
      enableSsl: true
    })

    Object.assign(settings.backup, {
      enableAutoBackup: true,
      frequency: 'daily',
      retentionCount: 7
    })

    console.log('✅ 设置已恢复默认值')
  }
}

const sendTestEmail = async () => {
  if (!testEmail.value) {
    alert('请输入测试邮箱地址')
    return
  }

  emailTesting.value = true
  try {
    // 模拟发送测试邮件
    await new Promise(resolve => setTimeout(resolve, 2000))

    console.log('✅ 测试邮件发送成功')
    alert(`测试邮件已发送到 ${testEmail.value}`)
    testEmail.value = ''
  } catch (error) {
    console.error('❌ 发送测试邮件失败:', error)
    alert('发送失败: ' + (error as Error).message)
  } finally {
    emailTesting.value = false
  }
}

const createBackup = async () => {
  backupLoading.value = true
  try {
    // 模拟创建备份
    await new Promise(resolve => setTimeout(resolve, 3000))

    const now = new Date()
    const timestamp = now.toISOString().replace(/[:.]/g, '-').slice(0, 19)
    const newBackup = {
      id: Date.now().toString(),
      name: `backup_${timestamp}.zip`,
      size: '2.8 MB',
      created_at: now.toISOString()
    }

    backupHistory.value.unshift(newBackup)

    console.log('✅ 备份创建成功')
    alert('备份创建成功！')
  } catch (error) {
    console.error('❌ 创建备份失败:', error)
    alert('创建备份失败: ' + (error as Error).message)
  } finally {
    backupLoading.value = false
  }
}

const downloadBackup = (backup: any) => {
  // 模拟下载备份文件
  const link = document.createElement('a')
  link.href = '#'
  link.download = backup.name
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)

  console.log('✅ 备份下载开始:', backup.name)
}

const deleteBackup = (backup: any) => {
  if (confirm(`确定要删除备份文件 "${backup.name}" 吗？此操作不可撤销。`)) {
    const index = backupHistory.value.findIndex(b => b.id === backup.id)
    if (index > -1) {
      backupHistory.value.splice(index, 1)
      console.log('✅ 备份删除成功')
    }
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(async () => {
  console.log('✅ 系统设置页面已加载')

  // 检查管理员权限
  if (!authStore.isAdmin) {
    alert('您没有权限访问此页面')
    return
  }

  // 保存原始设置
  originalSettings.value = JSON.parse(JSON.stringify(settings))
})
</script>

<style scoped>
/* 开关样式 */
.peer:checked ~ div {
  background-color: #3b82f6;
}

.peer:checked ~ div:after {
  transform: translateX(100%);
  border-color: white;
}

/* 选项卡样式 */
.tab-active {
  border-bottom-color: #3b82f6;
  color: #3b82f6;
}

/* 表单样式 */
input:focus,
textarea:focus,
select:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 备份历史样式 */
.backup-item:hover {
  background-color: #f9fafb;
}
</style>