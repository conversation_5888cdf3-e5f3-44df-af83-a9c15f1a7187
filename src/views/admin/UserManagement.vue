<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h2 class="text-2xl font-bold text-gray-900">用户管理</h2>
      <p class="text-gray-600 mt-1">管理系统用户和权限</p>
    </div>

    <!-- 用户列表 -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
      <div v-if="loading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>

      <div v-else-if="users.length === 0" class="text-center py-12">
        <UserGroupIcon class="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无用户</h3>
        <p class="text-gray-500">系统中还没有用户</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                用户信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                角色
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                创建时间
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="user in users" :key="user.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div class="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                      <UserIcon class="h-6 w-6 text-gray-600" />
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">
                      {{ user.name }}
                    </div>
                    <div class="text-sm text-gray-500">
                      {{ user.email }}
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                  :class="getRoleClass(user.role)"
                >
                  {{ getRoleText(user.role) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                  :class="user.is_active !== false ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                >
                  {{ user.is_active !== false ? '正常' : '禁用' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(user.created_at) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button
                  v-if="user.id !== authStore.user?.id"
                  @click="toggleUserStatus(user)"
                  :class="user.is_active !== false ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'"
                >
                  {{ user.is_active !== false ? '禁用' : '启用' }}
                </button>
                <span v-else class="text-gray-400">当前用户</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 用户统计 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <UserGroupIcon class="h-8 w-8 text-blue-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">总用户数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ users.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <ShieldCheckIcon class="h-8 w-8 text-red-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">管理员</p>
            <p class="text-2xl font-semibold text-gray-900">{{ adminCount }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <PencilIcon class="h-8 w-8 text-green-600" />
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-500">编辑</p>
            <p class="text-2xl font-semibold text-gray-900">{{ editorCount }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import {
  UserGroupIcon,
  UserIcon,
  ShieldCheckIcon,
  PencilIcon,
} from '@heroicons/vue/24/outline'
import { useAuthStore } from '@/stores/auth'
import { userApi } from '@/lib/vue-api'
import type { User } from '@/types/vue-types'

// Store
const authStore = useAuthStore()

// Reactive data
const users = ref<User[]>([])
const loading = ref(true)

// Computed
const adminCount = computed(() => 
  users.value.filter(user => user.role === 'admin').length
)

const editorCount = computed(() => 
  users.value.filter(user => user.role === 'editor').length
)

// Methods
const formatDate = (dateString: string) => {
  return format(new Date(dateString), 'yyyy-MM-dd', { locale: zhCN })
}

const getRoleClass = (role: string) => {
  return role === 'admin' 
    ? 'bg-red-100 text-red-800' 
    : 'bg-blue-100 text-blue-800'
}

const getRoleText = (role: string) => {
  return role === 'admin' ? '管理员' : '编辑'
}

const toggleUserStatus = async (user: User) => {
  const action = user.is_active !== false ? '禁用' : '启用'
  if (confirm(`确定要${action}用户"${user.name}"吗？`)) {
    try {
      // 这里应该调用API更新用户状态
      // 由于使用模拟数据，我们直接更新本地状态
      user.is_active = user.is_active === false
      console.log(`${action}用户:`, user.name)
    } catch (error) {
      alert(`${action}用户失败: ` + (error as Error).message)
    }
  }
}

const loadUsers = async () => {
  try {
    loading.value = true
    const data = await userApi.getAllUsers()
    users.value = data
  } catch (error) {
    console.error('加载用户失败:', error)
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadUsers()
})
</script>
