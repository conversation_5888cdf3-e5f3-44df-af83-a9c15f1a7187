<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">日志管理</h1>
        <p class="text-gray-600 mt-1">系统操作日志和审计记录</p>
      </div>
      
      <div class="flex space-x-3">
        <button
          @click="refreshLogs"
          :disabled="loading"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
        >
          <svg class="h-4 w-4 mr-2" :class="{ 'animate-spin': loading }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          刷新
        </button>
        <button
          @click="exportLogs"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          导出日志
        </button>
        <button
          @click="showCleanupModal = true"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
        >
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
          清理日志
        </button>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <div class="grid grid-cols-1 md:grid-cols-5 gap-4">
        <!-- 搜索框 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索用户或操作..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <!-- 日志级别 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">级别</label>
          <select
            v-model="selectedLevel"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部级别</option>
            <option value="info">信息</option>
            <option value="warning">警告</option>
            <option value="error">错误</option>
            <option value="critical">严重</option>
          </select>
        </div>

        <!-- 操作类型 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">操作类型</label>
          <select
            v-model="selectedAction"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部操作</option>
            <option value="login">登录</option>
            <option value="logout">登出</option>
            <option value="create">创建</option>
            <option value="update">更新</option>
            <option value="delete">删除</option>
            <option value="approve">审批</option>
            <option value="reject">拒绝</option>
          </select>
        </div>

        <!-- 时间范围 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">时间范围</label>
          <select
            v-model="selectedTimeRange"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部时间</option>
            <option value="today">今天</option>
            <option value="week">本周</option>
            <option value="month">本月</option>
            <option value="quarter">本季度</option>
          </select>
        </div>

        <!-- 用户筛选 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">用户</label>
          <select
            v-model="selectedUser"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部用户</option>
            <option value="admin">系统管理员</option>
            <option value="editor">新闻编辑</option>
            <option value="user">普通用户</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总日志数</p>
            <p class="text-2xl font-bold text-gray-900">{{ filteredLogs.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
            <svg class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">今日操作</p>
            <p class="text-2xl font-bold text-gray-900">{{ todayLogsCount }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
            <svg class="h-5 w-5 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">警告/错误</p>
            <p class="text-2xl font-bold text-gray-900">{{ warningErrorCount }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
            <svg class="h-5 w-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">活跃用户</p>
            <p class="text-2xl font-bold text-gray-900">{{ activeUsersCount }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志列表 -->
    <div class="bg-white rounded-lg shadow-sm">
      <!-- 加载状态 -->
      <div v-if="loading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>

      <!-- 日志表格 -->
      <div v-else-if="filteredLogs.length > 0" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">时间</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">级别</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">用户</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">描述</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP地址</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="log in paginatedLogs" :key="log.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDateTime(log.created_at) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getLevelClass(log.level)"
                >
                  {{ getLevelText(log.level) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-xs font-semibold">
                    {{ log.user_name.charAt(0).toUpperCase() }}
                  </div>
                  <div class="ml-3">
                    <div class="text-sm font-medium text-gray-900">{{ log.user_name }}</div>
                    <div class="text-sm text-gray-500">{{ log.user_role }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getActionClass(log.action)"
                >
                  {{ getActionText(log.action) }}
                </span>
              </td>
              <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                {{ log.description }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ log.ip_address }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button
                  @click="viewLogDetail(log)"
                  class="text-indigo-600 hover:text-indigo-900"
                >
                  详情
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 空状态 -->
      <div v-else class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无日志记录</h3>
        <p class="mt-1 text-sm text-gray-500">没有找到符合条件的日志</p>
      </div>

      <!-- 分页 -->
      <div v-if="filteredLogs.length > pageSize" class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <button
            @click="currentPage > 1 && (currentPage--)"
            :disabled="currentPage <= 1"
            class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            上一页
          </button>
          <button
            @click="currentPage < totalPages && (currentPage++)"
            :disabled="currentPage >= totalPages"
            class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            下一页
          </button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              显示第 <span class="font-medium">{{ (currentPage - 1) * pageSize + 1 }}</span> 到 
              <span class="font-medium">{{ Math.min(currentPage * pageSize, filteredLogs.length) }}</span> 条，
              共 <span class="font-medium">{{ filteredLogs.length }}</span> 条记录
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
              <button
                @click="currentPage > 1 && (currentPage--)"
                :disabled="currentPage <= 1"
                class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
              >
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              <span class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                {{ currentPage }} / {{ totalPages }}
              </span>
              <button
                @click="currentPage < totalPages && (currentPage++)"
                :disabled="currentPage >= totalPages"
                class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
              >
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>

    <!-- 日志详情模态框 -->
    <LogDetailModal
      v-if="showDetailModal"
      :log="selectedLog"
      @close="showDetailModal = false"
    />

    <!-- 清理日志模态框 -->
    <LogCleanupModal
      v-if="showCleanupModal"
      @close="showCleanupModal = false"
      @confirm="handleCleanupLogs"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import LogDetailModal from '@/components/LogDetailModal.vue'
import LogCleanupModal from '@/components/LogCleanupModal.vue'

// Composables
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const searchQuery = ref('')
const selectedLevel = ref('')
const selectedAction = ref('')
const selectedTimeRange = ref('')
const selectedUser = ref('')
const showDetailModal = ref(false)
const showCleanupModal = ref(false)
const selectedLog = ref(null)
const currentPage = ref(1)
const pageSize = ref(20)

// 模拟日志数据
const logs = ref([
  {
    id: '1',
    level: 'info',
    action: 'login',
    user_name: '系统管理员',
    user_role: '管理员',
    description: '用户登录系统',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    created_at: '2024-12-15T09:30:00Z',
    details: {
      session_id: 'sess_123456789',
      login_method: 'password',
      success: true
    }
  },
  {
    id: '2',
    level: 'info',
    action: 'create',
    user_name: '新闻编辑',
    user_role: '编辑',
    description: '创建新闻：荣联科技发布新一代AI解决方案',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    created_at: '2024-12-15T10:15:00Z',
    details: {
      resource_type: 'news',
      resource_id: 'news_001',
      title: '荣联科技发布新一代AI解决方案',
      category: '产品发布'
    }
  },
  {
    id: '3',
    level: 'info',
    action: 'approve',
    user_name: '系统管理员',
    user_role: '管理员',
    description: '审批通过新闻：Vue 3.0最佳实践分享',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    created_at: '2024-12-15T11:20:00Z',
    details: {
      resource_type: 'news',
      resource_id: 'news_002',
      previous_status: 'pending',
      new_status: 'published',
      comment: '内容质量良好，批准发布'
    }
  },
  {
    id: '4',
    level: 'warning',
    action: 'update',
    user_name: '张三',
    user_role: '编辑',
    description: '尝试修改他人新闻被拒绝',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    created_at: '2024-12-15T12:45:00Z',
    details: {
      resource_type: 'news',
      resource_id: 'news_003',
      reason: '权限不足：只能编辑自己创建的新闻',
      attempted_action: 'edit_other_user_news'
    }
  },
  {
    id: '5',
    level: 'error',
    action: 'delete',
    user_name: '李四',
    user_role: '编辑',
    description: '删除新闻失败：新闻不存在',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
    created_at: '2024-12-15T13:10:00Z',
    details: {
      resource_type: 'news',
      resource_id: 'news_999',
      error_code: 'NOT_FOUND',
      error_message: '指定的新闻记录不存在'
    }
  },
  {
    id: '6',
    level: 'info',
    action: 'logout',
    user_name: '王五',
    user_role: '编辑',
    description: '用户退出系统',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    created_at: '2024-12-15T14:30:00Z',
    details: {
      session_duration: '2小时15分钟',
      logout_type: 'manual'
    }
  },
  {
    id: '7',
    level: 'critical',
    action: 'login',
    user_name: '未知用户',
    user_role: '未知',
    description: '多次登录失败，账户被锁定',
    ip_address: '***********',
    user_agent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
    created_at: '2024-12-15T15:45:00Z',
    details: {
      failed_attempts: 5,
      lockout_duration: '30分钟',
      attempted_username: 'admin',
      security_alert: true
    }
  },
  {
    id: '8',
    level: 'info',
    action: 'create',
    user_name: '系统管理员',
    user_role: '管理员',
    description: '创建新用户：赵六',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    created_at: '2024-12-15T16:00:00Z',
    details: {
      resource_type: 'user',
      resource_id: 'user_006',
      username: 'zhaoliu',
      role: 'editor',
      email: '<EMAIL>'
    }
  },
  {
    id: '9',
    level: 'info',
    action: 'update',
    user_name: '系统管理员',
    user_role: '管理员',
    description: '更新系统设置：启用双因子认证',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    created_at: '2024-12-15T16:30:00Z',
    details: {
      resource_type: 'system_settings',
      setting_key: 'enable_two_factor',
      old_value: false,
      new_value: true
    }
  },
  {
    id: '10',
    level: 'warning',
    action: 'reject',
    user_name: '系统管理员',
    user_role: '管理员',
    description: '拒绝新闻：内容不符合发布标准',
    ip_address: '*************',
    user_agent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
    created_at: '2024-12-15T17:15:00Z',
    details: {
      resource_type: 'news',
      resource_id: 'news_004',
      previous_status: 'pending',
      new_status: 'draft',
      reason: '内容质量不达标，需要重新编辑'
    }
  }
])

// 计算属性
const filteredLogs = computed(() => {
  let result = logs.value

  // 搜索过滤
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(log =>
      log.user_name.toLowerCase().includes(query) ||
      log.description.toLowerCase().includes(query) ||
      log.action.toLowerCase().includes(query)
    )
  }

  // 级别过滤
  if (selectedLevel.value) {
    result = result.filter(log => log.level === selectedLevel.value)
  }

  // 操作类型过滤
  if (selectedAction.value) {
    result = result.filter(log => log.action === selectedAction.value)
  }

  // 时间范围过滤
  if (selectedTimeRange.value) {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    result = result.filter(log => {
      const logDate = new Date(log.created_at)

      switch (selectedTimeRange.value) {
        case 'today':
          return logDate >= today
        case 'week':
          const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
          return logDate >= weekAgo
        case 'month':
          const monthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate())
          return logDate >= monthAgo
        case 'quarter':
          const quarterAgo = new Date(today.getFullYear(), today.getMonth() - 3, today.getDate())
          return logDate >= quarterAgo
        default:
          return true
      }
    })
  }

  // 用户过滤
  if (selectedUser.value) {
    result = result.filter(log => {
      switch (selectedUser.value) {
        case 'admin':
          return log.user_role === '管理员'
        case 'editor':
          return log.user_role === '编辑'
        case 'user':
          return log.user_role === '用户'
        default:
          return true
      }
    })
  }

  return result.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
})

const paginatedLogs = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredLogs.value.slice(start, end)
})

const totalPages = computed(() => {
  return Math.ceil(filteredLogs.value.length / pageSize.value)
})

const todayLogsCount = computed(() => {
  const today = new Date()
  const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate())
  return logs.value.filter(log => new Date(log.created_at) >= todayStart).length
})

const warningErrorCount = computed(() => {
  return logs.value.filter(log => log.level === 'warning' || log.level === 'error' || log.level === 'critical').length
})

const activeUsersCount = computed(() => {
  const uniqueUsers = new Set(logs.value.map(log => log.user_name))
  return uniqueUsers.size
})

// 方法
const refreshLogs = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    console.log('✅ 日志数据刷新成功')
  } catch (error) {
    console.error('❌ 刷新日志数据失败:', error)
  } finally {
    loading.value = false
  }
}

const exportLogs = () => {
  const exportData = filteredLogs.value.map(log => ({
    时间: formatDateTime(log.created_at),
    级别: getLevelText(log.level),
    用户: log.user_name,
    角色: log.user_role,
    操作: getActionText(log.action),
    描述: log.description,
    IP地址: log.ip_address
  }))

  const dataStr = JSON.stringify(exportData, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)

  const link = document.createElement('a')
  link.href = url
  link.download = `logs-export-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  console.log('✅ 日志导出成功')
}

const viewLogDetail = (log: any) => {
  selectedLog.value = log
  showDetailModal.value = true
}

const handleCleanupLogs = (options: any) => {
  console.log('✅ 清理日志:', options)
  // 这里实现日志清理逻辑
  showCleanupModal.value = false
}

const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const getLevelClass = (level: string) => {
  switch (level) {
    case 'info':
      return 'bg-blue-100 text-blue-800'
    case 'warning':
      return 'bg-yellow-100 text-yellow-800'
    case 'error':
      return 'bg-red-100 text-red-800'
    case 'critical':
      return 'bg-red-100 text-red-800 font-bold'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getLevelText = (level: string) => {
  switch (level) {
    case 'info':
      return '信息'
    case 'warning':
      return '警告'
    case 'error':
      return '错误'
    case 'critical':
      return '严重'
    default:
      return '未知'
  }
}

const getActionClass = (action: string) => {
  switch (action) {
    case 'login':
      return 'bg-green-100 text-green-800'
    case 'logout':
      return 'bg-gray-100 text-gray-800'
    case 'create':
      return 'bg-blue-100 text-blue-800'
    case 'update':
      return 'bg-yellow-100 text-yellow-800'
    case 'delete':
      return 'bg-red-100 text-red-800'
    case 'approve':
      return 'bg-green-100 text-green-800'
    case 'reject':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getActionText = (action: string) => {
  switch (action) {
    case 'login':
      return '登录'
    case 'logout':
      return '登出'
    case 'create':
      return '创建'
    case 'update':
      return '更新'
    case 'delete':
      return '删除'
    case 'approve':
      return '审批'
    case 'reject':
      return '拒绝'
    default:
      return '未知'
  }
}

// 生命周期
onMounted(async () => {
  console.log('✅ 日志管理页面已加载')

  // 检查管理员权限
  if (!authStore.isAdmin) {
    alert('您没有权限访问此页面')
    return
  }

  await refreshLogs()
})
</script>

<style scoped>
/* 表格样式 */
.table-fixed {
  table-layout: fixed;
}

/* 分页样式 */
.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 级别标签样式 */
.level-critical {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}
</style>
