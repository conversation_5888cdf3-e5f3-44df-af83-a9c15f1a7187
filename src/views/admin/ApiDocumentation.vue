<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">API 接口文档</h1>
        <p class="text-gray-600 mt-1">系统API接口文档和在线测试</p>
      </div>
      
      <div class="flex space-x-3">
        <button
          @click="exportApiDoc"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          导出文档
        </button>
        <button
          @click="showApiTester = true"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
          </svg>
          API 测试器
        </button>
      </div>
    </div>

    <!-- API 概览 -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <h2 class="text-lg font-semibold text-gray-900 mb-4">API 概览</h2>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="text-center p-4 bg-blue-50 rounded-lg">
          <div class="text-2xl font-bold text-blue-600">{{ apiStats.totalEndpoints }}</div>
          <div class="text-sm text-blue-600">总接口数</div>
        </div>
        <div class="text-center p-4 bg-green-50 rounded-lg">
          <div class="text-2xl font-bold text-green-600">{{ apiStats.publicEndpoints }}</div>
          <div class="text-sm text-green-600">公开接口</div>
        </div>
        <div class="text-center p-4 bg-yellow-50 rounded-lg">
          <div class="text-2xl font-bold text-yellow-600">{{ apiStats.authEndpoints }}</div>
          <div class="text-sm text-yellow-600">需认证接口</div>
        </div>
        <div class="text-center p-4 bg-purple-50 rounded-lg">
          <div class="text-2xl font-bold text-purple-600">{{ apiStats.adminEndpoints }}</div>
          <div class="text-sm text-purple-600">管理员接口</div>
        </div>
      </div>
    </div>

    <!-- API 分类导航 -->
    <div class="bg-white rounded-lg shadow-sm">
      <div class="border-b border-gray-200">
        <nav class="flex space-x-8 px-6" aria-label="Tabs">
          <button
            v-for="category in apiCategories"
            :key="category.id"
            @click="activeCategory = category.id"
            class="py-4 px-1 border-b-2 font-medium text-sm"
            :class="activeCategory === category.id 
              ? 'border-blue-500 text-blue-600' 
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'"
          >
            <div class="flex items-center">
              <component :is="category.icon" class="h-5 w-5 mr-2" />
              {{ category.name }}
              <span class="ml-2 bg-gray-100 text-gray-600 py-1 px-2 rounded-full text-xs">
                {{ category.count }}
              </span>
            </div>
          </button>
        </nav>
      </div>

      <!-- API 接口列表 -->
      <div class="p-6">
        <div class="space-y-4">
          <div
            v-for="endpoint in filteredEndpoints"
            :key="endpoint.id"
            class="border border-gray-200 rounded-lg overflow-hidden"
          >
            <!-- 接口头部 -->
            <div 
              class="p-4 cursor-pointer hover:bg-gray-50"
              @click="toggleEndpoint(endpoint.id)"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                  <span 
                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                    :class="getMethodClass(endpoint.method)"
                  >
                    {{ endpoint.method }}
                  </span>
                  <code class="text-sm font-mono text-gray-900">{{ endpoint.path }}</code>
                  <span class="text-sm text-gray-600">{{ endpoint.summary }}</span>
                </div>
                <div class="flex items-center space-x-2">
                  <span 
                    v-if="endpoint.auth"
                    class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-100 text-yellow-800"
                  >
                    🔒 需认证
                  </span>
                  <span 
                    v-if="endpoint.admin"
                    class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800"
                  >
                    👑 管理员
                  </span>
                  <svg 
                    class="h-5 w-5 text-gray-400 transform transition-transform"
                    :class="{ 'rotate-180': expandedEndpoints.includes(endpoint.id) }"
                    fill="none" 
                    viewBox="0 0 24 24" 
                    stroke="currentColor"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>

            <!-- 接口详情 -->
            <div v-if="expandedEndpoints.includes(endpoint.id)" class="border-t border-gray-200 bg-gray-50">
              <div class="p-6 space-y-6">
                <!-- 描述 -->
                <div>
                  <h4 class="text-sm font-medium text-gray-900 mb-2">接口描述</h4>
                  <p class="text-sm text-gray-600">{{ endpoint.description }}</p>
                </div>

                <!-- 请求参数 -->
                <div v-if="endpoint.parameters && endpoint.parameters.length > 0">
                  <h4 class="text-sm font-medium text-gray-900 mb-2">请求参数</h4>
                  <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                      <thead class="bg-gray-100">
                        <tr>
                          <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">参数名</th>
                          <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">类型</th>
                          <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">位置</th>
                          <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">必填</th>
                          <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">说明</th>
                        </tr>
                      </thead>
                      <tbody class="bg-white divide-y divide-gray-200">
                        <tr v-for="param in endpoint.parameters" :key="param.name">
                          <td class="px-4 py-2 text-sm font-mono text-gray-900">{{ param.name }}</td>
                          <td class="px-4 py-2 text-sm text-gray-600">{{ param.type }}</td>
                          <td class="px-4 py-2 text-sm text-gray-600">{{ param.in }}</td>
                          <td class="px-4 py-2 text-sm">
                            <span v-if="param.required" class="text-red-600">是</span>
                            <span v-else class="text-gray-400">否</span>
                          </td>
                          <td class="px-4 py-2 text-sm text-gray-600">{{ param.description }}</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                <!-- 请求示例 -->
                <div v-if="endpoint.requestExample">
                  <h4 class="text-sm font-medium text-gray-900 mb-2">请求示例</h4>
                  <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                    <pre class="text-sm text-green-400"><code>{{ endpoint.requestExample }}</code></pre>
                  </div>
                </div>

                <!-- 响应示例 -->
                <div v-if="endpoint.responseExample">
                  <h4 class="text-sm font-medium text-gray-900 mb-2">响应示例</h4>
                  <div class="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                    <pre class="text-sm text-blue-400"><code>{{ endpoint.responseExample }}</code></pre>
                  </div>
                </div>

                <!-- 状态码 -->
                <div v-if="endpoint.responses">
                  <h4 class="text-sm font-medium text-gray-900 mb-2">响应状态码</h4>
                  <div class="space-y-2">
                    <div 
                      v-for="response in endpoint.responses" 
                      :key="response.code"
                      class="flex items-center space-x-3"
                    >
                      <span 
                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                        :class="getStatusClass(response.code)"
                      >
                        {{ response.code }}
                      </span>
                      <span class="text-sm text-gray-600">{{ response.description }}</span>
                    </div>
                  </div>
                </div>

                <!-- 测试按钮 -->
                <div class="flex justify-end">
                  <button
                    @click="testEndpoint(endpoint)"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                  >
                    <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m2-7a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    测试接口
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="filteredEndpoints.length === 0" class="text-center py-12">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">暂无接口</h3>
          <p class="mt-1 text-sm text-gray-500">该分类下暂无API接口</p>
        </div>
      </div>
    </div>

    <!-- API 测试器模态框 -->
    <ApiTester
      v-if="showApiTester"
      :endpoint="testingEndpoint"
      @close="showApiTester = false"
      @test="handleApiTest"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import ApiTester from '@/components/ApiTester.vue'

// Composables
const authStore = useAuthStore()

// 响应式数据
const activeCategory = ref('auth')
const expandedEndpoints = ref<string[]>([])
const showApiTester = ref(false)
const testingEndpoint = ref(null)

// API 分类
const apiCategories = [
  { id: 'auth', name: '认证授权', icon: 'svg', count: 3 },
  { id: 'news', name: '新闻管理', icon: 'svg', count: 8 },
  { id: 'users', name: '用户管理', icon: 'svg', count: 6 },
  { id: 'categories', name: '分类管理', icon: 'svg', count: 4 },
  { id: 'system', name: '系统管理', icon: 'svg', count: 5 }
]

// API 接口数据
const apiEndpoints = ref([
  // 认证授权
  {
    id: 'auth-login',
    category: 'auth',
    method: 'POST',
    path: '/api/auth/login',
    summary: '用户登录',
    description: '用户通过邮箱和密码登录系统，返回访问令牌',
    auth: false,
    admin: false,
    parameters: [
      { name: 'email', type: 'string', in: 'body', required: true, description: '用户邮箱地址' },
      { name: 'password', type: 'string', in: 'body', required: true, description: '用户密码' }
    ],
    requestExample: `POST /api/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "admin123"
}`,
    responseExample: `{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "1",
      "name": "系统管理员",
      "email": "<EMAIL>",
      "role": "admin"
    }
  },
  "message": "登录成功"
}`,
    responses: [
      { code: '200', description: '登录成功' },
      { code: '401', description: '邮箱或密码错误' },
      { code: '422', description: '参数验证失败' }
    ]
  },
  {
    id: 'auth-logout',
    category: 'auth',
    method: 'POST',
    path: '/api/auth/logout',
    summary: '用户登出',
    description: '用户退出登录，使当前令牌失效',
    auth: true,
    admin: false,
    parameters: [],
    requestExample: `POST /api/auth/logout
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`,
    responseExample: `{
  "success": true,
  "message": "登出成功"
}`,
    responses: [
      { code: '200', description: '登出成功' },
      { code: '401', description: '未授权访问' }
    ]
  },
  {
    id: 'auth-profile',
    category: 'auth',
    method: 'GET',
    path: '/api/auth/profile',
    summary: '获取用户信息',
    description: '获取当前登录用户的详细信息',
    auth: true,
    admin: false,
    parameters: [],
    requestExample: `GET /api/auth/profile
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`,
    responseExample: `{
  "success": true,
  "data": {
    "id": "1",
    "name": "系统管理员",
    "email": "<EMAIL>",
    "role": "admin",
    "created_at": "2024-01-01T00:00:00Z",
    "last_login": "2024-12-15T10:30:00Z"
  }
}`,
    responses: [
      { code: '200', description: '获取成功' },
      { code: '401', description: '未授权访问' }
    ]
  },

  // 新闻管理
  {
    id: 'news-list',
    category: 'news',
    method: 'GET',
    path: '/api/news',
    summary: '获取新闻列表',
    description: '获取新闻列表，支持分页和筛选',
    auth: false,
    admin: false,
    parameters: [
      { name: 'page', type: 'integer', in: 'query', required: false, description: '页码，默认为1' },
      { name: 'limit', type: 'integer', in: 'query', required: false, description: '每页数量，默认为10' },
      { name: 'category', type: 'string', in: 'query', required: false, description: '分类筛选' },
      { name: 'status', type: 'string', in: 'query', required: false, description: '状态筛选' },
      { name: 'search', type: 'string', in: 'query', required: false, description: '搜索关键词' }
    ],
    requestExample: `GET /api/news?page=1&limit=10&category=tech&status=published`,
    responseExample: `{
  "success": true,
  "data": {
    "items": [
      {
        "id": "1",
        "title": "荣联科技发布新一代AI解决方案",
        "excerpt": "本次发布的AI解决方案...",
        "content": "详细内容...",
        "category": "产品发布",
        "status": "published",
        "author": "新闻编辑",
        "created_at": "2024-12-15T10:00:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 5,
      "total_items": 50,
      "per_page": 10
    }
  }
}`,
    responses: [
      { code: '200', description: '获取成功' },
      { code: '422', description: '参数验证失败' }
    ]
  },
  {
    id: 'news-detail',
    category: 'news',
    method: 'GET',
    path: '/api/news/{id}',
    summary: '获取新闻详情',
    description: '根据ID获取新闻的详细信息',
    auth: false,
    admin: false,
    parameters: [
      { name: 'id', type: 'string', in: 'path', required: true, description: '新闻ID' }
    ],
    requestExample: `GET /api/news/1`,
    responseExample: `{
  "success": true,
  "data": {
    "id": "1",
    "title": "荣联科技发布新一代AI解决方案",
    "excerpt": "本次发布的AI解决方案...",
    "content": "详细内容...",
    "category": "产品发布",
    "tags": ["AI", "产品", "技术"],
    "status": "published",
    "author": "新闻编辑",
    "views": 1250,
    "created_at": "2024-12-15T10:00:00Z",
    "updated_at": "2024-12-15T10:00:00Z"
  }
}`,
    responses: [
      { code: '200', description: '获取成功' },
      { code: '404', description: '新闻不存在' }
    ]
  },
  {
    id: 'news-create',
    category: 'news',
    method: 'POST',
    path: '/api/news',
    summary: '创建新闻',
    description: '创建一篇新的新闻文章',
    auth: true,
    admin: false,
    parameters: [
      { name: 'title', type: 'string', in: 'body', required: true, description: '新闻标题' },
      { name: 'excerpt', type: 'string', in: 'body', required: true, description: '新闻摘要' },
      { name: 'content', type: 'string', in: 'body', required: true, description: '新闻内容' },
      { name: 'category', type: 'string', in: 'body', required: true, description: '新闻分类' },
      { name: 'tags', type: 'array', in: 'body', required: false, description: '新闻标签' }
    ],
    requestExample: `POST /api/news
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "title": "新闻标题",
  "excerpt": "新闻摘要",
  "content": "新闻详细内容...",
  "category": "技术分享",
  "tags": ["技术", "分享"]
}`,
    responseExample: `{
  "success": true,
  "data": {
    "id": "2",
    "title": "新闻标题",
    "status": "draft",
    "created_at": "2024-12-15T11:00:00Z"
  },
  "message": "新闻创建成功"
}`,
    responses: [
      { code: '201', description: '创建成功' },
      { code: '401', description: '未授权访问' },
      { code: '422', description: '参数验证失败' }
    ]
  },

  // 用户管理
  {
    id: 'users-list',
    category: 'users',
    method: 'GET',
    path: '/api/users',
    summary: '获取用户列表',
    description: '获取系统用户列表（仅管理员）',
    auth: true,
    admin: true,
    parameters: [
      { name: 'page', type: 'integer', in: 'query', required: false, description: '页码' },
      { name: 'limit', type: 'integer', in: 'query', required: false, description: '每页数量' },
      { name: 'role', type: 'string', in: 'query', required: false, description: '角色筛选' }
    ],
    requestExample: `GET /api/users?page=1&limit=10&role=editor
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`,
    responseExample: `{
  "success": true,
  "data": {
    "items": [
      {
        "id": "1",
        "name": "张三",
        "email": "<EMAIL>",
        "role": "editor",
        "status": "active",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "current_page": 1,
      "total_pages": 2,
      "total_items": 15
    }
  }
}`,
    responses: [
      { code: '200', description: '获取成功' },
      { code: '401', description: '未授权访问' },
      { code: '403', description: '权限不足' }
    ]
  }
])

// 计算属性
const filteredEndpoints = computed(() => {
  return apiEndpoints.value.filter(endpoint => endpoint.category === activeCategory.value)
})

const apiStats = computed(() => {
  const total = apiEndpoints.value.length
  const publicEndpoints = apiEndpoints.value.filter(e => !e.auth).length
  const authEndpoints = apiEndpoints.value.filter(e => e.auth && !e.admin).length
  const adminEndpoints = apiEndpoints.value.filter(e => e.admin).length

  return {
    totalEndpoints: total,
    publicEndpoints,
    authEndpoints,
    adminEndpoints
  }
})

// 方法
const toggleEndpoint = (endpointId: string) => {
  const index = expandedEndpoints.value.indexOf(endpointId)
  if (index > -1) {
    expandedEndpoints.value.splice(index, 1)
  } else {
    expandedEndpoints.value.push(endpointId)
  }
}

const getMethodClass = (method: string) => {
  switch (method.toUpperCase()) {
    case 'GET':
      return 'bg-green-100 text-green-800'
    case 'POST':
      return 'bg-blue-100 text-blue-800'
    case 'PUT':
      return 'bg-yellow-100 text-yellow-800'
    case 'DELETE':
      return 'bg-red-100 text-red-800'
    case 'PATCH':
      return 'bg-purple-100 text-purple-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusClass = (code: string) => {
  const statusCode = parseInt(code)
  if (statusCode >= 200 && statusCode < 300) {
    return 'bg-green-100 text-green-800'
  } else if (statusCode >= 400 && statusCode < 500) {
    return 'bg-yellow-100 text-yellow-800'
  } else if (statusCode >= 500) {
    return 'bg-red-100 text-red-800'
  }
  return 'bg-gray-100 text-gray-800'
}

const testEndpoint = (endpoint: any) => {
  testingEndpoint.value = endpoint
  showApiTester.value = true
}

const handleApiTest = (result: any) => {
  console.log('API测试结果:', result)
}

const exportApiDoc = () => {
  const docData = {
    title: '荣联科技新闻系统 API 文档',
    version: '1.0.0',
    baseUrl: 'https://api.ronglian.com',
    description: '荣联科技新闻发布系统的RESTful API接口文档',
    categories: apiCategories,
    endpoints: apiEndpoints.value,
    exportTime: new Date().toISOString()
  }

  const dataStr = JSON.stringify(docData, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)

  const link = document.createElement('a')
  link.href = url
  link.download = `api-documentation-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  console.log('✅ API文档导出成功')
}

// 生命周期
onMounted(() => {
  console.log('✅ API文档页面已加载')

  // 检查管理员权限
  if (!authStore.isAdmin) {
    alert('您没有权限访问此页面')
    return
  }
})
</script>

<style scoped>
/* 代码块样式 */
pre {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
}

/* 表格样式 */
table {
  font-size: 13px;
}

/* 展开动画 */
.transform {
  transition: transform 0.2s ease-in-out;
}

/* 方法标签样式 */
.method-tag {
  font-weight: 600;
  letter-spacing: 0.05em;
}
</style>
