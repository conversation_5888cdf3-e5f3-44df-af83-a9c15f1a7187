<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">分类管理</h1>
        <p class="text-gray-600 mt-1">管理新闻分类和标签</p>
      </div>
      
      <div class="flex space-x-3">
        <button
          @click="showCreateCategoryModal = true"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          添加分类
        </button>
        <button
          @click="showCreateTagModal = true"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
          </svg>
          添加标签
        </button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总分类</p>
            <p class="text-2xl font-bold text-gray-900">{{ categories.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
            <svg class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">启用分类</p>
            <p class="text-2xl font-bold text-gray-900">{{ activeCategories.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
            <svg class="h-5 w-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总标签</p>
            <p class="text-2xl font-bold text-gray-900">{{ tags.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
            <svg class="h-5 w-5 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">热门标签</p>
            <p class="text-2xl font-bold text-gray-900">{{ popularTags.length }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 分类和标签管理 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 分类管理 -->
      <div class="bg-white rounded-lg shadow-sm">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-900">新闻分类</h2>
            <button
              @click="showCreateCategoryModal = true"
              class="text-sm text-blue-600 hover:text-blue-800"
            >
              + 添加分类
            </button>
          </div>
        </div>

        <div class="p-6">
          <!-- 搜索框 -->
          <div class="mb-4">
            <input
              v-model="categorySearchQuery"
              type="text"
              placeholder="搜索分类..."
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <!-- 分类列表 -->
          <div v-if="filteredCategories.length > 0" class="space-y-3">
            <div
              v-for="category in filteredCategories"
              :key="category.id"
              class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
            >
              <div class="flex items-center space-x-3">
                <div 
                  class="w-4 h-4 rounded-full"
                  :style="{ backgroundColor: category.color }"
                ></div>
                <div>
                  <h3 class="font-medium text-gray-900">{{ category.name }}</h3>
                  <p class="text-sm text-gray-500">{{ category.description }}</p>
                  <div class="flex items-center space-x-4 text-xs text-gray-400 mt-1">
                    <span>{{ category.news_count || 0 }} 篇新闻</span>
                    <span>{{ formatDate(category.created_at) }}</span>
                  </div>
                </div>
              </div>
              
              <div class="flex items-center space-x-2">
                <span 
                  class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"
                  :class="category.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                >
                  {{ category.status === 'active' ? '启用' : '禁用' }}
                </span>
                <button
                  @click="editCategory(category)"
                  class="text-indigo-600 hover:text-indigo-900 text-sm"
                >
                  编辑
                </button>
                <button
                  @click="toggleCategoryStatus(category)"
                  class="text-yellow-600 hover:text-yellow-900 text-sm"
                >
                  {{ category.status === 'active' ? '禁用' : '启用' }}
                </button>
                <button
                  @click="deleteCategory(category)"
                  class="text-red-600 hover:text-red-900 text-sm"
                >
                  删除
                </button>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无分类</h3>
            <p class="mt-1 text-sm text-gray-500">创建第一个新闻分类</p>
          </div>
        </div>
      </div>

      <!-- 标签管理 -->
      <div class="bg-white rounded-lg shadow-sm">
        <div class="px-6 py-4 border-b border-gray-200">
          <div class="flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-900">新闻标签</h2>
            <button
              @click="showCreateTagModal = true"
              class="text-sm text-blue-600 hover:text-blue-800"
            >
              + 添加标签
            </button>
          </div>
        </div>

        <div class="p-6">
          <!-- 搜索框 -->
          <div class="mb-4">
            <input
              v-model="tagSearchQuery"
              type="text"
              placeholder="搜索标签..."
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <!-- 标签列表 -->
          <div v-if="filteredTags.length > 0" class="space-y-3">
            <div
              v-for="tag in filteredTags"
              :key="tag.id"
              class="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
            >
              <div class="flex items-center space-x-3">
                <span 
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :style="{ backgroundColor: tag.color + '20', color: tag.color }"
                >
                  {{ tag.name }}
                </span>
                <div>
                  <p class="text-sm text-gray-500">{{ tag.description }}</p>
                  <div class="flex items-center space-x-4 text-xs text-gray-400 mt-1">
                    <span>{{ tag.usage_count || 0 }} 次使用</span>
                    <span>{{ formatDate(tag.created_at) }}</span>
                  </div>
                </div>
              </div>
              
              <div class="flex items-center space-x-2">
                <button
                  @click="editTag(tag)"
                  class="text-indigo-600 hover:text-indigo-900 text-sm"
                >
                  编辑
                </button>
                <button
                  @click="deleteTag(tag)"
                  class="text-red-600 hover:text-red-900 text-sm"
                >
                  删除
                </button>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="text-center py-8">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900">暂无标签</h3>
            <p class="mt-1 text-sm text-gray-500">创建第一个新闻标签</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 分类编辑模态框 -->
    <CategoryEditor
      v-if="showCreateCategoryModal || showEditCategoryModal"
      :category="editingCategory"
      :is-edit="showEditCategoryModal"
      @close="closeCategoryEditor"
      @save="handleSaveCategory"
    />

    <!-- 标签编辑模态框 -->
    <TagEditor
      v-if="showCreateTagModal || showEditTagModal"
      :tag="editingTag"
      :is-edit="showEditTagModal"
      @close="closeTagEditor"
      @save="handleSaveTag"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import CategoryEditor from '@/components/CategoryEditor.vue'
import TagEditor from '@/components/TagEditor.vue'

// Composables
const authStore = useAuthStore()

// 响应式数据
const categorySearchQuery = ref('')
const tagSearchQuery = ref('')
const showCreateCategoryModal = ref(false)
const showEditCategoryModal = ref(false)
const showCreateTagModal = ref(false)
const showEditTagModal = ref(false)
const editingCategory = ref(null)
const editingTag = ref(null)
const loading = ref(false)

// 模拟分类数据
const categories = ref([
  {
    id: '1',
    name: '公司新闻',
    description: '公司内部新闻和公告',
    color: '#3b82f6',
    status: 'active',
    news_count: 8,
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: '产品发布',
    description: '新产品发布和更新',
    color: '#10b981',
    status: 'active',
    news_count: 5,
    created_at: '2024-02-01T00:00:00Z'
  },
  {
    id: '3',
    name: '技术分享',
    description: '技术文章和经验分享',
    color: '#f59e0b',
    status: 'active',
    news_count: 12,
    created_at: '2024-03-01T00:00:00Z'
  },
  {
    id: '4',
    name: '行业动态',
    description: '行业新闻和趋势分析',
    color: '#8b5cf6',
    status: 'active',
    news_count: 3,
    created_at: '2024-04-01T00:00:00Z'
  },
  {
    id: '5',
    name: '活动通知',
    description: '公司活动和会议通知',
    color: '#ef4444',
    status: 'inactive',
    news_count: 0,
    created_at: '2024-05-01T00:00:00Z'
  }
])

// 模拟标签数据
const tags = ref([
  {
    id: '1',
    name: '重要',
    description: '重要新闻标签',
    color: '#ef4444',
    usage_count: 15,
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: '热点',
    description: '热点新闻标签',
    color: '#f59e0b',
    usage_count: 12,
    created_at: '2024-01-15T00:00:00Z'
  },
  {
    id: '3',
    name: '技术',
    description: '技术相关标签',
    color: '#3b82f6',
    usage_count: 8,
    created_at: '2024-02-01T00:00:00Z'
  },
  {
    id: '4',
    name: '产品',
    description: '产品相关标签',
    color: '#10b981',
    usage_count: 6,
    created_at: '2024-02-15T00:00:00Z'
  },
  {
    id: '5',
    name: '公告',
    description: '公告类标签',
    color: '#8b5cf6',
    usage_count: 4,
    created_at: '2024-03-01T00:00:00Z'
  },
  {
    id: '6',
    name: '活动',
    description: '活动相关标签',
    color: '#06b6d4',
    usage_count: 3,
    created_at: '2024-03-15T00:00:00Z'
  },
  {
    id: '7',
    name: '合作',
    description: '合作相关标签',
    color: '#84cc16',
    usage_count: 2,
    created_at: '2024-04-01T00:00:00Z'
  },
  {
    id: '8',
    name: '创新',
    description: '创新相关标签',
    color: '#f97316',
    usage_count: 5,
    created_at: '2024-04-15T00:00:00Z'
  }
])

// 计算属性
const filteredCategories = computed(() => {
  let result = categories.value

  if (categorySearchQuery.value) {
    const query = categorySearchQuery.value.toLowerCase()
    result = result.filter(category =>
      category.name.toLowerCase().includes(query) ||
      category.description.toLowerCase().includes(query)
    )
  }

  return result.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
})

const filteredTags = computed(() => {
  let result = tags.value

  if (tagSearchQuery.value) {
    const query = tagSearchQuery.value.toLowerCase()
    result = result.filter(tag =>
      tag.name.toLowerCase().includes(query) ||
      tag.description.toLowerCase().includes(query)
    )
  }

  return result.sort((a, b) => (b.usage_count || 0) - (a.usage_count || 0))
})

const activeCategories = computed(() => {
  return categories.value.filter(category => category.status === 'active')
})

const popularTags = computed(() => {
  return tags.value.filter(tag => (tag.usage_count || 0) >= 5)
})

// 方法
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 分类管理方法
const editCategory = (category: any) => {
  editingCategory.value = { ...category }
  showEditCategoryModal.value = true
}

const deleteCategory = async (category: any) => {
  if (category.news_count > 0) {
    alert(`无法删除分类"${category.name}"，该分类下还有 ${category.news_count} 篇新闻`)
    return
  }

  if (confirm(`确定要删除分类"${category.name}"吗？此操作不可恢复。`)) {
    try {
      const index = categories.value.findIndex(c => c.id === category.id)
      if (index > -1) {
        categories.value.splice(index, 1)
        console.log('✅ 分类删除成功')
      }
    } catch (error) {
      console.error('❌ 删除分类失败:', error)
      alert('删除失败: ' + (error as Error).message)
    }
  }
}

const toggleCategoryStatus = async (category: any) => {
  const newStatus = category.status === 'active' ? 'inactive' : 'active'
  const action = newStatus === 'inactive' ? '禁用' : '启用'

  if (confirm(`确定要${action}分类"${category.name}"吗？`)) {
    try {
      category.status = newStatus
      console.log(`✅ 分类${action}成功`)
    } catch (error) {
      console.error(`❌ ${action}分类失败:`, error)
      alert(`${action}失败: ` + (error as Error).message)
    }
  }
}

const closeCategoryEditor = () => {
  showCreateCategoryModal.value = false
  showEditCategoryModal.value = false
  editingCategory.value = null
}

const handleSaveCategory = async (categoryData: any) => {
  try {
    if (showEditCategoryModal.value && editingCategory.value) {
      // 更新分类
      const index = categories.value.findIndex(c => c.id === editingCategory.value.id)
      if (index > -1) {
        categories.value[index] = { ...categories.value[index], ...categoryData }
        console.log('✅ 分类更新成功')
      }
    } else {
      // 创建分类
      const newCategory = {
        id: `category_${Date.now()}`,
        ...categoryData,
        status: 'active',
        news_count: 0,
        created_at: new Date().toISOString()
      }
      categories.value.unshift(newCategory)
      console.log('✅ 分类创建成功')
    }
    closeCategoryEditor()
  } catch (error) {
    console.error('❌ 保存分类失败:', error)
    alert('保存失败: ' + (error as Error).message)
  }
}

// 标签管理方法
const editTag = (tag: any) => {
  editingTag.value = { ...tag }
  showEditTagModal.value = true
}

const deleteTag = async (tag: any) => {
  if (tag.usage_count > 0) {
    if (!confirm(`标签"${tag.name}"已被使用 ${tag.usage_count} 次，删除后相关新闻的标签也会被移除。确定要删除吗？`)) {
      return
    }
  }

  if (confirm(`确定要删除标签"${tag.name}"吗？此操作不可恢复。`)) {
    try {
      const index = tags.value.findIndex(t => t.id === tag.id)
      if (index > -1) {
        tags.value.splice(index, 1)
        console.log('✅ 标签删除成功')
      }
    } catch (error) {
      console.error('❌ 删除标签失败:', error)
      alert('删除失败: ' + (error as Error).message)
    }
  }
}

const closeTagEditor = () => {
  showCreateTagModal.value = false
  showEditTagModal.value = false
  editingTag.value = null
}

const handleSaveTag = async (tagData: any) => {
  try {
    if (showEditTagModal.value && editingTag.value) {
      // 更新标签
      const index = tags.value.findIndex(t => t.id === editingTag.value.id)
      if (index > -1) {
        tags.value[index] = { ...tags.value[index], ...tagData }
        console.log('✅ 标签更新成功')
      }
    } else {
      // 创建标签
      const newTag = {
        id: `tag_${Date.now()}`,
        ...tagData,
        usage_count: 0,
        created_at: new Date().toISOString()
      }
      tags.value.unshift(newTag)
      console.log('✅ 标签创建成功')
    }
    closeTagEditor()
  } catch (error) {
    console.error('❌ 保存标签失败:', error)
    alert('保存失败: ' + (error as Error).message)
  }
}

// 生命周期
onMounted(async () => {
  console.log('✅ 分类管理页面已加载')

  // 检查管理员权限
  if (!authStore.isAdmin) {
    alert('您没有权限访问此页面')
    return
  }
})
</script>
