<template>
  <div class="space-y-6">
    <!-- 页面标题和导航 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <button
          @click="goBack"
          class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          返回新闻管理
        </button>
        <div>
          <h1 class="text-2xl font-bold text-gray-900">创建新闻</h1>
          <p class="text-gray-600 mt-1">填写新闻信息并发布</p>
        </div>
      </div>
      
      <!-- 快速操作 -->
      <div class="flex space-x-3">
        <button
          @click="saveDraft"
          :disabled="loading || !form.title.trim()"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
        >
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          {{ loading ? '保存中...' : '保存草稿' }}
        </button>
        <button
          @click="handleSubmit"
          :disabled="loading || !isFormValid"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
        >
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          {{ loading ? '创建中...' : '创建新闻' }}
        </button>
      </div>
    </div>

    <!-- 主要表单 -->
    <div class="bg-white rounded-lg shadow-sm">
      <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
        <!-- 基本信息 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- 标题 -->
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              标题 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.title"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入新闻标题"
            />
            <p class="mt-1 text-sm text-gray-500">{{ form.title.length }}/100 字符</p>
          </div>

          <!-- 分类 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              分类 <span class="text-red-500">*</span>
            </label>
            <select
              v-model="form.category_id"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">请选择分类</option>
              <option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>

          <!-- 状态 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              发布状态 <span class="text-red-500">*</span>
            </label>
            <select
              v-model="form.status"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="draft">草稿</option>
              <option value="pending" v-if="authStore.isAdmin || authStore.isEditor">提交审批</option>
              <option value="published" v-if="authStore.isAdmin">直接发布</option>
            </select>
            <p class="mt-1 text-sm text-gray-500">
              <span v-if="form.status === 'draft'">保存为草稿，稍后可以继续编辑</span>
              <span v-else-if="form.status === 'pending'">提交给管理员审批后发布</span>
              <span v-else-if="form.status === 'published'">立即发布到网站</span>
            </p>
          </div>
        </div>

        <!-- 摘要 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            摘要 <span class="text-red-500">*</span>
          </label>
          <textarea
            v-model="form.excerpt"
            required
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="请输入新闻摘要，建议100-200字"
          ></textarea>
          <p class="mt-1 text-sm text-gray-500">{{ form.excerpt.length }}/200 字符</p>
        </div>

        <!-- 标签管理 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">标签</label>
          <div class="space-y-3">
            <!-- 已添加的标签 -->
            <div v-if="form.tags.length > 0" class="flex flex-wrap gap-2">
              <span
                v-for="(tag, index) in form.tags"
                :key="index"
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
              >
                {{ tag }}
                <button
                  type="button"
                  @click="removeTag(index)"
                  class="ml-2 text-blue-600 hover:text-blue-800"
                >
                  <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </span>
            </div>
            
            <!-- 添加标签 -->
            <div class="flex">
              <input
                v-model="newTag"
                type="text"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="输入标签后按回车或点击添加"
                @keyup.enter="addTag"
              />
              <button
                type="button"
                @click="addTag"
                class="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700"
              >
                添加
              </button>
            </div>
            
            <!-- 推荐标签 -->
            <div>
              <p class="text-sm text-gray-500 mb-2">推荐标签：</p>
              <div class="flex flex-wrap gap-2">
                <button
                  v-for="tag in recommendedTags"
                  :key="tag"
                  type="button"
                  @click="addRecommendedTag(tag)"
                  :disabled="form.tags.includes(tag)"
                  class="px-2 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {{ tag }}
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 内容编辑器 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            内容 <span class="text-red-500">*</span>
          </label>
          
          <!-- 工具栏 -->
          <div class="border border-gray-300 rounded-t-md bg-gray-50 p-3">
            <div class="flex flex-wrap gap-2">
              <!-- 文本格式 -->
              <div class="flex border border-gray-300 rounded">
                <button
                  type="button"
                  @click="formatText('bold')"
                  class="px-3 py-1 text-sm hover:bg-gray-100 border-r border-gray-300"
                  title="粗体"
                >
                  <strong>B</strong>
                </button>
                <button
                  type="button"
                  @click="formatText('italic')"
                  class="px-3 py-1 text-sm hover:bg-gray-100 border-r border-gray-300"
                  title="斜体"
                >
                  <em>I</em>
                </button>
                <button
                  type="button"
                  @click="formatText('underline')"
                  class="px-3 py-1 text-sm hover:bg-gray-100"
                  title="下划线"
                >
                  <u>U</u>
                </button>
              </div>

              <!-- 列表 -->
              <div class="flex border border-gray-300 rounded">
                <button
                  type="button"
                  @click="formatText('insertUnorderedList')"
                  class="px-3 py-1 text-sm hover:bg-gray-100 border-r border-gray-300"
                  title="无序列表"
                >
                  • 列表
                </button>
                <button
                  type="button"
                  @click="formatText('insertOrderedList')"
                  class="px-3 py-1 text-sm hover:bg-gray-100"
                  title="有序列表"
                >
                  1. 列表
                </button>
              </div>

              <!-- 插入 -->
              <div class="flex border border-gray-300 rounded">
                <button
                  type="button"
                  @click="insertLink"
                  class="px-3 py-1 text-sm hover:bg-gray-100 border-r border-gray-300"
                  title="插入链接"
                >
                  🔗 链接
                </button>
                <button
                  type="button"
                  @click="insertImage"
                  class="px-3 py-1 text-sm hover:bg-gray-100"
                  title="插入图片"
                >
                  🖼️ 图片
                </button>
              </div>

              <!-- 清除格式 -->
              <button
                type="button"
                @click="formatText('removeFormat')"
                class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
                title="清除格式"
              >
                清除格式
              </button>
            </div>
          </div>

          <!-- 编辑器 -->
          <div
            ref="editor"
            contenteditable="true"
            class="w-full min-h-[400px] px-4 py-3 border border-gray-300 border-t-0 rounded-b-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            @input="updateContent"
            @paste="handlePaste"
            placeholder="请输入新闻内容..."
          ></div>
          
          <!-- 编辑器提示 -->
          <div class="mt-2 flex justify-between text-sm text-gray-500">
            <span>支持富文本编辑，可以插入图片和链接</span>
            <span>{{ getContentLength() }} 字符</span>
          </div>
        </div>
      </form>
    </div>

    <!-- 预览区域 -->
    <div v-if="form.title || form.content" class="bg-white rounded-lg shadow-sm">
      <div class="p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">预览</h3>
        <div class="border border-gray-200 rounded-lg p-4">
          <h4 class="text-xl font-bold text-gray-900 mb-2">{{ form.title || '新闻标题' }}</h4>
          <div class="flex items-center space-x-4 text-sm text-gray-500 mb-4">
            <span>分类: {{ getCategoryName(form.category_id) || '未选择' }}</span>
            <span>状态: {{ getStatusText(form.status) }}</span>
            <span>作者: {{ authStore.user?.name }}</span>
          </div>
          <p v-if="form.excerpt" class="text-gray-600 mb-4 italic">{{ form.excerpt }}</p>
          <div class="prose max-w-none" v-html="form.content || '新闻内容'"></div>
          <div v-if="form.tags.length > 0" class="mt-4 pt-4 border-t border-gray-200">
            <div class="flex flex-wrap gap-2">
              <span
                v-for="tag in form.tags"
                :key="tag"
                class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
              >
                {{ tag }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { useNewsStore } from '@/stores/news'
import { useAuthStore } from '@/stores/auth'

// Composables
const router = useRouter()
const newsStore = useNewsStore()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const newTag = ref('')
const editor = ref<HTMLElement>()

// 分类数据
const categories = ref([
  { id: '1', name: '公司新闻' },
  { id: '2', name: '产品发布' },
  { id: '3', name: '技术分享' },
  { id: '4', name: '行业动态' }
])

// 推荐标签
const recommendedTags = ref([
  '重要', '热点', '技术', '产品', '公告', '活动', '合作', '创新', '发展', '成就'
])

// 表单数据
const form = reactive({
  title: '',
  content: '',
  excerpt: '',
  category_id: '',
  status: 'draft',
  tags: [] as string[]
})

// 计算属性
const isFormValid = computed(() => {
  return form.title.trim() &&
         form.content.trim() &&
         form.excerpt.trim() &&
         form.category_id &&
         form.status
})

// 方法
const goBack = () => {
  if (hasUnsavedChanges()) {
    if (confirm('您有未保存的更改，确定要离开吗？')) {
      router.push('/admin/news')
    }
  } else {
    router.push('/admin/news')
  }
}

const hasUnsavedChanges = () => {
  return form.title.trim() || form.content.trim() || form.excerpt.trim() || form.tags.length > 0
}

const updateContent = () => {
  if (editor.value) {
    form.content = editor.value.innerHTML
  }
}

const getContentLength = () => {
  if (editor.value) {
    return editor.value.textContent?.length || 0
  }
  return 0
}

const formatText = (command: string) => {
  document.execCommand(command, false)
  editor.value?.focus()
}

const insertLink = () => {
  const url = prompt('请输入链接地址:')
  if (url) {
    document.execCommand('createLink', false, url)
    editor.value?.focus()
  }
}

const insertImage = () => {
  const url = prompt('请输入图片地址:')
  if (url) {
    document.execCommand('insertImage', false, url)
    editor.value?.focus()
  }
}

const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const text = event.clipboardData?.getData('text/plain') || ''
  document.execCommand('insertText', false, text)
}

const addTag = () => {
  const tag = newTag.value.trim()
  if (tag && !form.tags.includes(tag) && form.tags.length < 10) {
    form.tags.push(tag)
    newTag.value = ''
  }
}

const addRecommendedTag = (tag: string) => {
  if (!form.tags.includes(tag) && form.tags.length < 10) {
    form.tags.push(tag)
  }
}

const removeTag = (index: number) => {
  form.tags.splice(index, 1)
}

const getCategoryName = (categoryId: string) => {
  const category = categories.value.find(c => c.id === categoryId)
  return category?.name || ''
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'published':
      return '已发布'
    case 'pending':
      return '待审批'
    case 'draft':
      return '草稿'
    default:
      return '未知'
  }
}

const saveDraft = async () => {
  if (!form.title.trim()) {
    alert('请输入标题')
    return
  }

  loading.value = true
  try {
    const data = {
      ...form,
      status: 'draft',
      author_id: authStore.user?.id || 'user1'
    }

    await newsStore.createNews(data)
    console.log('✅ 草稿保存成功')

    // 显示成功消息并询问是否继续编辑
    if (confirm('草稿保存成功！是否返回新闻管理页面？')) {
      router.push('/admin/news')
    }
  } catch (error) {
    console.error('❌ 保存草稿失败:', error)
    alert('保存失败: ' + (error as Error).message)
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  if (!isFormValid.value) {
    alert('请填写所有必填字段')
    return
  }

  loading.value = true
  try {
    const data = {
      ...form,
      author_id: authStore.user?.id || 'user1'
    }

    await newsStore.createNews(data)
    console.log('✅ 新闻创建成功')

    // 根据状态显示不同的成功消息
    let message = ''
    if (form.status === 'draft') {
      message = '新闻已保存为草稿！'
    } else if (form.status === 'pending') {
      message = '新闻已提交审批！'
    } else if (form.status === 'published') {
      message = '新闻已发布！'
    }

    alert(message)
    router.push('/admin/news')
  } catch (error) {
    console.error('❌ 创建新闻失败:', error)
    alert('创建失败: ' + (error as Error).message)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(async () => {
  console.log('✅ 新闻创建页面已加载')

  // 检查用户是否已登录
  if (!authStore.isAuthenticated) {
    router.push('/')
    return
  }

  await nextTick()

  // 设置编辑器占位符
  if (editor.value) {
    editor.value.innerHTML = '<p>请输入新闻内容...</p>'

    // 监听编辑器焦点，清除占位符
    editor.value.addEventListener('focus', () => {
      if (editor.value?.innerHTML === '<p>请输入新闻内容...</p>') {
        editor.value.innerHTML = ''
      }
    })

    // 监听编辑器失焦，恢复占位符
    editor.value.addEventListener('blur', () => {
      if (!editor.value?.textContent?.trim()) {
        editor.value.innerHTML = '<p>请输入新闻内容...</p>'
      }
    })
  }
})

// 页面离开前的确认
window.addEventListener('beforeunload', (event) => {
  if (hasUnsavedChanges()) {
    event.preventDefault()
    event.returnValue = ''
  }
})
</script>

<style scoped>
/* 编辑器样式 */
[contenteditable="true"]:focus {
  outline: none;
}

[contenteditable="true"] p {
  margin: 0.5em 0;
}

[contenteditable="true"] ul,
[contenteditable="true"] ol {
  margin: 0.5em 0;
  padding-left: 2em;
}

[contenteditable="true"] img {
  max-width: 100%;
  height: auto;
  margin: 0.5em 0;
}

[contenteditable="true"] a {
  color: #3b82f6;
  text-decoration: underline;
}

/* 预览样式 */
.prose {
  color: #374151;
  line-height: 1.75;
}

.prose p {
  margin: 1em 0;
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  margin: 1.5em 0 0.5em 0;
  font-weight: 600;
  line-height: 1.25;
}

.prose ul,
.prose ol {
  margin: 1em 0;
  padding-left: 2em;
}

.prose img {
  max-width: 100%;
  height: auto;
  margin: 1em 0;
  border-radius: 0.5rem;
}

.prose a {
  color: #3b82f6;
  text-decoration: underline;
}
</style>
