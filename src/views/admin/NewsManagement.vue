<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex justify-between items-center">
      <h2 class="text-2xl font-bold text-gray-900">新闻管理</h2>
      <router-link
        to="/admin/news/create"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <PlusIcon class="h-4 w-4 mr-2" />
        创建新闻
      </router-link>
    </div>

    <!-- 筛选器 -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
          <select
            v-model="filters.status"
            @change="handleFilterChange"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">所有状态</option>
            <option value="draft">草稿</option>
            <option value="pending">待审批</option>
            <option value="published">已发布</option>
            <option value="rejected">已拒绝</option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">分类</label>
          <select
            v-model="filters.category_id"
            @change="handleFilterChange"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">所有分类</option>
            <option v-for="category in categories" :key="category.id" :value="category.id">
              {{ category.name }}
            </option>
          </select>
        </div>
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
          <input
            v-model="filters.search"
            @input="handleFilterChange"
            type="text"
            placeholder="搜索标题或内容..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
        <div class="flex items-end">
          <button
            @click="resetFilters"
            class="w-full px-3 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            重置筛选
          </button>
        </div>
      </div>
    </div>

    <!-- 新闻列表 -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
      <div v-if="newsStore.loading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>

      <div v-else-if="newsStore.news.length === 0" class="text-center py-12">
        <NewspaperIcon class="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无新闻</h3>
        <p class="text-gray-500">开始创建您的第一篇新闻吧</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                标题
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                分类
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                作者
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                创建时间
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                浏览量
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="item in newsStore.news" :key="item.id" class="hover:bg-gray-50">
              <td class="px-6 py-4">
                <div class="text-sm font-medium text-gray-900 max-w-xs truncate">
                  {{ item.title }}
                </div>
                <div class="text-sm text-gray-500 max-w-xs truncate">
                  {{ item.excerpt }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-sm text-gray-900">{{ item.category.name }}</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex px-2 py-1 text-xs font-semibold rounded-full"
                  :class="getStatusClass(item.status)"
                >
                  {{ getStatusText(item.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ item.author.name }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(item.created_at) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ item.view_count }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                <button
                  @click="handleView(item)"
                  class="text-blue-600 hover:text-blue-900"
                >
                  查看
                </button>
                <router-link
                  :to="`/admin/news/${item.id}/edit`"
                  class="text-indigo-600 hover:text-indigo-900"
                >
                  编辑
                </router-link>
                <button
                  @click="handleDelete(item)"
                  class="text-red-600 hover:text-red-900"
                >
                  删除
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- 新闻查看器 -->
    <NewsViewer
      v-if="showViewer && selectedNews"
      :news="selectedNews"
      @close="handleCloseViewer"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { PlusIcon, NewspaperIcon } from '@heroicons/vue/24/outline'
import { useNewsStore } from '@/stores/news'
import { categoryApi } from '@/lib/vue-api'
import NewsViewer from '@/components/NewsViewer.vue'
import type { NewsWithDetails, Category, NewsFilters } from '@/types/vue-types'

// Store
const newsStore = useNewsStore()

// Reactive data
const categories = ref<Category[]>([])
const selectedNews = ref<NewsWithDetails | null>(null)
const showViewer = ref(false)

const filters = reactive<NewsFilters>({
  status: '',
  category_id: '',
  search: '',
  page: 1,
  limit: 50,
})

// Methods
const formatDate = (dateString: string) => {
  return format(new Date(dateString), 'yyyy-MM-dd HH:mm', { locale: zhCN })
}

const getStatusClass = (status: string) => {
  const classes = {
    draft: 'bg-gray-100 text-gray-800',
    pending: 'bg-yellow-100 text-yellow-800',
    published: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800',
  }
  return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status: string) => {
  const texts = {
    draft: '草稿',
    pending: '待审批',
    published: '已发布',
    rejected: '已拒绝',
  }
  return texts[status as keyof typeof texts] || status
}

const handleFilterChange = () => {
  newsStore.fetchNews(filters)
}

const resetFilters = () => {
  Object.assign(filters, {
    status: '',
    category_id: '',
    search: '',
    page: 1,
    limit: 50,
  })
  handleFilterChange()
}

const handleView = (news: NewsWithDetails) => {
  selectedNews.value = news
  showViewer.value = true
  newsStore.incrementViewCount(news.id)
}

const handleCloseViewer = () => {
  showViewer.value = false
  selectedNews.value = null
}

const handleDelete = async (news: NewsWithDetails) => {
  if (confirm(`确定要删除新闻"${news.title}"吗？`)) {
    try {
      await newsStore.deleteNews(news.id)
    } catch (error) {
      alert('删除失败: ' + (error as Error).message)
    }
  }
}

const loadCategories = async () => {
  try {
    const data = await categoryApi.getCategories()
    categories.value = data
  } catch (error) {
    console.error('加载分类失败:', error)
  }
}

// Lifecycle
onMounted(async () => {
  await Promise.all([
    newsStore.fetchNews(filters),
    loadCategories()
  ])
})
</script>
