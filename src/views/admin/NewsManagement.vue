<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">新闻管理</h1>
        <p class="text-gray-600 mt-1">管理所有新闻内容，包括创建、编辑和删除</p>
      </div>
      <button
        @click="createNews"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
      >
        <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        创建新闻
      </button>
    </div>

    <!-- 筛选和搜索 -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <!-- 搜索框 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">搜索</label>
          <input
            v-model="searchQuery"
            type="text"
            placeholder="搜索标题或内容..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <!-- 状态筛选 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">状态</label>
          <select
            v-model="selectedStatus"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部状态</option>
            <option value="published">已发布</option>
            <option value="pending">待审批</option>
            <option value="draft">草稿</option>
          </select>
        </div>

        <!-- 分类筛选 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">分类</label>
          <select
            v-model="selectedCategory"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">全部分类</option>
            <option v-for="category in categories" :key="category.id" :value="category.id">
              {{ category.name }}
            </option>
          </select>
        </div>

        <!-- 刷新按钮 -->
        <div class="flex items-end">
          <button
            @click="refreshNews"
            :disabled="newsStore.loading"
            class="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
          >
            <svg class="h-4 w-4 mr-2" :class="{ 'animate-spin': newsStore.loading }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            刷新
          </button>
        </div>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总新闻</p>
            <p class="text-2xl font-bold text-gray-900">{{ newsStore.news.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
            <svg class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">已发布</p>
            <p class="text-2xl font-bold text-gray-900">{{ newsStore.publishedNews.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
            <svg class="h-5 w-5 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">待审批</p>
            <p class="text-2xl font-bold text-gray-900">{{ newsStore.pendingNews.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
            <svg class="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">草稿</p>
            <p class="text-2xl font-bold text-gray-900">{{ newsStore.draftNews.length }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 新闻列表 -->
    <div class="bg-white rounded-lg shadow-sm">
      <!-- 加载状态 -->
      <div v-if="newsStore.loading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>

      <!-- 新闻表格 -->
      <div v-else-if="filteredNews.length > 0" class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">标题</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">分类</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">作者</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">状态</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">浏览量</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">创建时间</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="news in filteredNews" :key="news.id" class="hover:bg-gray-50">
              <td class="px-6 py-4">
                <div class="max-w-xs">
                  <p class="text-sm font-medium text-gray-900 truncate">{{ news.title }}</p>
                  <p class="text-sm text-gray-500 truncate">{{ news.excerpt }}</p>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {{ news.category.name }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ news.author.name }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getStatusClass(news.status)"
                >
                  {{ getStatusText(news.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ news.view_count }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(news.created_at) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                <button
                  @click="viewNews(news)"
                  class="text-blue-600 hover:text-blue-900"
                >
                  查看
                </button>
                <button
                  @click="editNews(news)"
                  class="text-indigo-600 hover:text-indigo-900"
                >
                  编辑
                </button>
                <button
                  v-if="news.status === 'draft' && (authStore.isAdmin || news.author_id === authStore.user?.id)"
                  @click="publishNews(news)"
                  class="text-green-600 hover:text-green-900"
                >
                  发布
                </button>
                <button
                  v-if="authStore.isAdmin || news.author_id === authStore.user?.id"
                  @click="deleteNews(news)"
                  class="text-red-600 hover:text-red-900"
                >
                  删除
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 空状态 -->
      <div v-else class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">暂无新闻</h3>
        <p class="mt-1 text-sm text-gray-500">开始创建您的第一篇新闻吧</p>
        <div class="mt-6">
          <button
            @click="createNews"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            <svg class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            创建新闻
          </button>
        </div>
      </div>
    </div>

    <!-- 新闻查看模态框 -->
    <div v-if="showViewModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="showViewModal = false">
      <div class="bg-white rounded-lg p-6 w-full max-w-4xl max-h-[90vh] overflow-y-auto" @click.stop>
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold">{{ viewingNews?.title }}</h3>
          <button @click="showViewModal = false" class="text-gray-400 hover:text-gray-600">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="prose max-w-none" v-html="viewingNews?.content"></div>
        <div class="mt-4 pt-4 border-t border-gray-200 text-sm text-gray-500">
          <p>作者: {{ viewingNews?.author.name }} | 分类: {{ viewingNews?.category.name }} | 浏览量: {{ viewingNews?.view_count }}</p>
          <p>创建时间: {{ formatDate(viewingNews?.created_at) }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useNewsStore } from '@/stores/news'
import { useAuthStore } from '@/stores/auth'

// Composables
const newsStore = useNewsStore()
const authStore = useAuthStore()

// 响应式数据
const searchQuery = ref('')
const selectedStatus = ref('')
const selectedCategory = ref('')
const showViewModal = ref(false)
const viewingNews = ref(null)

// 分类数据
const categories = ref([
  { id: '1', name: '公司新闻' },
  { id: '2', name: '产品发布' },
  { id: '3', name: '技术分享' },
  { id: '4', name: '行业动态' }
])

// 计算属性
const filteredNews = computed(() => {
  let result = newsStore.news

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    result = result.filter(item =>
      item.title.toLowerCase().includes(query) ||
      item.excerpt.toLowerCase().includes(query)
    )
  }

  if (selectedStatus.value) {
    result = result.filter(item => item.status === selectedStatus.value)
  }

  if (selectedCategory.value) {
    result = result.filter(item => item.category_id === selectedCategory.value)
  }

  // 按创建时间倒序排列
  return result.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
})

// 方法
const refreshNews = async () => {
  try {
    await newsStore.fetchNews()
    console.log('✅ 新闻数据刷新成功')
  } catch (error) {
    console.error('❌ 刷新新闻失败:', error)
  }
}

const createNews = () => {
  alert('创建新闻功能开发中...\n\n在完整版本中，这里会打开新闻编辑器。')
}

const viewNews = (news: any) => {
  viewingNews.value = news
  showViewModal.value = true
  newsStore.incrementViewCount(news.id)
}

const editNews = (news: any) => {
  alert(`编辑新闻功能开发中...\n新闻: ${news.title}`)
}

const publishNews = async (news: any) => {
  if (confirm(`确定要发布新闻"${news.title}"吗？`)) {
    try {
      await newsStore.updateNews(news.id, { status: 'pending' })
      console.log('✅ 新闻已提交审批')
    } catch (error) {
      console.error('❌ 发布新闻失败:', error)
    }
  }
}

const deleteNews = async (news: any) => {
  if (confirm(`确定要删除新闻"${news.title}"吗？此操作不可恢复。`)) {
    try {
      await newsStore.deleteNews(news.id)
      console.log('✅ 新闻删除成功')
    } catch (error) {
      console.error('❌ 删除新闻失败:', error)
    }
  }
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'published':
      return 'bg-green-100 text-green-800'
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'draft':
      return 'bg-gray-100 text-gray-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'published':
      return '已发布'
    case 'pending':
      return '待审批'
    case 'draft':
      return '草稿'
    default:
      return '未知'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(async () => {
  console.log('✅ 新闻管理页面已加载')
  await refreshNews()
})
</script>