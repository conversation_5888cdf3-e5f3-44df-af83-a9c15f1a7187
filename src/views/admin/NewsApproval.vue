<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div>
      <h2 class="text-2xl font-bold text-gray-900">新闻审批</h2>
      <p class="text-gray-600 mt-1">审批待发布的新闻内容</p>
    </div>

    <!-- 待审批新闻列表 -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
      <div v-if="newsStore.loading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>

      <div v-else-if="pendingNews.length === 0" class="text-center py-12">
        <CheckCircleIcon class="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无待审批新闻</h3>
        <p class="text-gray-500">所有新闻都已处理完毕</p>
      </div>

      <div v-else>
        <div class="divide-y divide-gray-200">
          <div
            v-for="item in pendingNews"
            :key="item.id"
            class="p-6 hover:bg-gray-50"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1 min-w-0">
                <!-- 标题和摘要 -->
                <div class="mb-4">
                  <h3 class="text-lg font-semibold text-gray-900 mb-2">
                    {{ item.title }}
                  </h3>
                  <p class="text-gray-600 text-sm line-clamp-2">
                    {{ item.excerpt }}
                  </p>
                </div>

                <!-- 元信息 -->
                <div class="flex flex-wrap items-center gap-4 text-sm text-gray-500 mb-4">
                  <div class="flex items-center">
                    <FolderIcon class="h-4 w-4 mr-1" />
                    <span>{{ item.category.name }}</span>
                  </div>
                  <div class="flex items-center">
                    <UserIcon class="h-4 w-4 mr-1" />
                    <span>{{ item.author.name }}</span>
                  </div>
                  <div class="flex items-center">
                    <CalendarIcon class="h-4 w-4 mr-1" />
                    <span>{{ formatDate(item.created_at) }}</span>
                  </div>
                </div>

                <!-- 标签 -->
                <div v-if="item.tags && item.tags.length > 0" class="mb-4">
                  <div class="flex flex-wrap gap-2">
                    <span
                      v-for="(tag, index) in item.tags"
                      :key="index"
                      class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                    >
                      {{ tag }}
                    </span>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="flex items-center space-x-3">
                  <button
                    @click="handleView(item)"
                    class="text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    查看详情
                  </button>
                  <button
                    @click="handleApprove(item)"
                    :disabled="loading"
                    class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                  >
                    <CheckIcon class="h-4 w-4 mr-1" />
                    批准
                  </button>
                  <button
                    @click="handleReject(item)"
                    :disabled="loading"
                    class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                  >
                    <XMarkIcon class="h-4 w-4 mr-1" />
                    拒绝
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新闻查看器 -->
    <NewsViewer
      v-if="showViewer && selectedNews"
      :news="selectedNews"
      @close="handleCloseViewer"
    />

    <!-- 拒绝原因模态框 -->
    <TransitionRoot appear :show="showRejectModal" as="template">
      <Dialog as="div" class="relative z-50" @close="showRejectModal = false">
        <TransitionChild
          as="template"
          enter="duration-300 ease-out"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="duration-200 ease-in"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div class="fixed inset-0 overflow-y-auto">
          <div class="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as="template"
              enter="duration-300 ease-out"
              enter-from="opacity-0 scale-95"
              enter-to="opacity-100 scale-100"
              leave="duration-200 ease-in"
              leave-from="opacity-100 scale-100"
              leave-to="opacity-0 scale-95"
            >
              <DialogPanel class="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900 mb-4">
                  拒绝新闻
                </DialogTitle>

                <div class="mb-4">
                  <label for="rejectReason" class="block text-sm font-medium text-gray-700 mb-2">
                    拒绝原因
                  </label>
                  <textarea
                    id="rejectReason"
                    v-model="rejectReason"
                    rows="4"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="请输入拒绝原因..."
                  ></textarea>
                </div>

                <div class="flex justify-end space-x-3">
                  <button
                    @click="showRejectModal = false"
                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200"
                  >
                    取消
                  </button>
                  <button
                    @click="confirmReject"
                    :disabled="!rejectReason.trim() || loading"
                    class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 disabled:opacity-50"
                  >
                    确认拒绝
                  </button>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </TransitionRoot>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import {
  CheckCircleIcon,
  FolderIcon,
  UserIcon,
  CalendarIcon,
  CheckIcon,
  XMarkIcon,
} from '@heroicons/vue/24/outline'
import {
  Dialog,
  DialogPanel,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
} from '@headlessui/vue'
import { useNewsStore } from '@/stores/news'
import NewsViewer from '@/components/NewsViewer.vue'
import type { NewsWithDetails } from '@/types/vue-types'

// Store
const newsStore = useNewsStore()

// Reactive data
const selectedNews = ref<NewsWithDetails | null>(null)
const showViewer = ref(false)
const showRejectModal = ref(false)
const rejectingNews = ref<NewsWithDetails | null>(null)
const rejectReason = ref('')
const loading = ref(false)

// Computed
const pendingNews = computed(() => newsStore.pendingNews)

// Methods
const formatDate = (dateString: string) => {
  return format(new Date(dateString), 'yyyy-MM-dd HH:mm', { locale: zhCN })
}

const handleView = (news: NewsWithDetails) => {
  selectedNews.value = news
  showViewer.value = true
}

const handleCloseViewer = () => {
  showViewer.value = false
  selectedNews.value = null
}

const handleApprove = async (news: NewsWithDetails) => {
  if (confirm(`确定要批准新闻"${news.title}"吗？`)) {
    try {
      loading.value = true
      await newsStore.approveNews(news.id)
    } catch (error) {
      alert('批准失败: ' + (error as Error).message)
    } finally {
      loading.value = false
    }
  }
}

const handleReject = (news: NewsWithDetails) => {
  rejectingNews.value = news
  rejectReason.value = ''
  showRejectModal.value = true
}

const confirmReject = async () => {
  if (!rejectingNews.value || !rejectReason.value.trim()) return
  
  try {
    loading.value = true
    await newsStore.rejectNews(rejectingNews.value.id, rejectReason.value.trim())
    showRejectModal.value = false
    rejectingNews.value = null
    rejectReason.value = ''
  } catch (error) {
    alert('拒绝失败: ' + (error as Error).message)
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  newsStore.fetchNews({ status: 'pending' })
})
</script>
