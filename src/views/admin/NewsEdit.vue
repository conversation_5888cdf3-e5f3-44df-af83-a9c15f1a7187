<template>
  <div class="space-y-6">
    <!-- 页面标题和导航 -->
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-4">
        <button
          @click="goBack"
          class="inline-flex items-center px-3 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
        >
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          返回新闻管理
        </button>
        <div>
          <h1 class="text-2xl font-bold text-gray-900">编辑新闻</h1>
          <p class="text-gray-600 mt-1">修改新闻信息</p>
        </div>
      </div>
      
      <!-- 快速操作 -->
      <div class="flex space-x-3">
        <button
          @click="saveDraft"
          :disabled="loading || !form.title.trim()"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
        >
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          {{ loading ? '保存中...' : '保存草稿' }}
        </button>
        <button
          @click="handleSubmit"
          :disabled="loading || !isFormValid"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
        >
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          {{ loading ? '更新中...' : '更新新闻' }}
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="pageLoading" class="flex justify-center items-center h-64">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>

    <!-- 新闻不存在 -->
    <div v-else-if="!newsItem" class="text-center py-12">
      <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
      </svg>
      <h3 class="mt-2 text-sm font-medium text-gray-900">新闻不存在</h3>
      <p class="mt-1 text-sm text-gray-500">请检查新闻ID是否正确</p>
      <div class="mt-6">
        <button
          @click="goBack"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          返回新闻管理
        </button>
      </div>
    </div>

    <!-- 主要表单 -->
    <div v-else class="bg-white rounded-lg shadow-sm">
      <!-- 新闻信息 -->
      <div class="p-6 border-b border-gray-200 bg-gray-50">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
          <div>
            <span class="text-gray-500">创建时间:</span>
            <span class="ml-2 font-medium">{{ formatDate(newsItem.created_at) }}</span>
          </div>
          <div>
            <span class="text-gray-500">最后修改:</span>
            <span class="ml-2 font-medium">{{ formatDate(newsItem.updated_at) }}</span>
          </div>
          <div>
            <span class="text-gray-500">浏览量:</span>
            <span class="ml-2 font-medium">{{ newsItem.view_count }}</span>
          </div>
        </div>
      </div>

      <form @submit.prevent="handleSubmit" class="p-6 space-y-6">
        <!-- 基本信息 -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- 标题 -->
          <div class="md:col-span-2">
            <label class="block text-sm font-medium text-gray-700 mb-2">
              标题 <span class="text-red-500">*</span>
            </label>
            <input
              v-model="form.title"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="请输入新闻标题"
            />
            <p class="mt-1 text-sm text-gray-500">{{ form.title.length }}/100 字符</p>
          </div>

          <!-- 分类 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              分类 <span class="text-red-500">*</span>
            </label>
            <select
              v-model="form.category_id"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">请选择分类</option>
              <option v-for="category in categories" :key="category.id" :value="category.id">
                {{ category.name }}
              </option>
            </select>
          </div>

          <!-- 状态 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              发布状态 <span class="text-red-500">*</span>
            </label>
            <select
              v-model="form.status"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="draft">草稿</option>
              <option value="pending" v-if="authStore.isAdmin || authStore.isEditor">提交审批</option>
              <option value="published" v-if="authStore.isAdmin">直接发布</option>
            </select>
            <p class="mt-1 text-sm text-gray-500">
              <span v-if="form.status === 'draft'">保存为草稿，稍后可以继续编辑</span>
              <span v-else-if="form.status === 'pending'">提交给管理员审批后发布</span>
              <span v-else-if="form.status === 'published'">立即发布到网站</span>
            </p>
          </div>
        </div>

        <!-- 摘要 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            摘要 <span class="text-red-500">*</span>
          </label>
          <textarea
            v-model="form.excerpt"
            required
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="请输入新闻摘要，建议100-200字"
          ></textarea>
          <p class="mt-1 text-sm text-gray-500">{{ form.excerpt.length }}/200 字符</p>
        </div>

        <!-- 标签管理 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">标签</label>
          <div class="space-y-3">
            <!-- 已添加的标签 -->
            <div v-if="form.tags.length > 0" class="flex flex-wrap gap-2">
              <span
                v-for="(tag, index) in form.tags"
                :key="index"
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
              >
                {{ tag }}
                <button
                  type="button"
                  @click="removeTag(index)"
                  class="ml-2 text-blue-600 hover:text-blue-800"
                >
                  <svg class="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </span>
            </div>
            
            <!-- 添加标签 -->
            <div class="flex">
              <input
                v-model="newTag"
                type="text"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="输入标签后按回车或点击添加"
                @keyup.enter="addTag"
              />
              <button
                type="button"
                @click="addTag"
                class="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700"
              >
                添加
              </button>
            </div>
          </div>
        </div>

        <!-- 内容编辑器 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">
            内容 <span class="text-red-500">*</span>
          </label>
          
          <!-- 工具栏 -->
          <div class="border border-gray-300 rounded-t-md bg-gray-50 p-3">
            <div class="flex flex-wrap gap-2">
              <!-- 文本格式 -->
              <div class="flex border border-gray-300 rounded">
                <button
                  type="button"
                  @click="formatText('bold')"
                  class="px-3 py-1 text-sm hover:bg-gray-100 border-r border-gray-300"
                  title="粗体"
                >
                  <strong>B</strong>
                </button>
                <button
                  type="button"
                  @click="formatText('italic')"
                  class="px-3 py-1 text-sm hover:bg-gray-100 border-r border-gray-300"
                  title="斜体"
                >
                  <em>I</em>
                </button>
                <button
                  type="button"
                  @click="formatText('underline')"
                  class="px-3 py-1 text-sm hover:bg-gray-100"
                  title="下划线"
                >
                  <u>U</u>
                </button>
              </div>

              <!-- 列表 -->
              <div class="flex border border-gray-300 rounded">
                <button
                  type="button"
                  @click="formatText('insertUnorderedList')"
                  class="px-3 py-1 text-sm hover:bg-gray-100 border-r border-gray-300"
                  title="无序列表"
                >
                  • 列表
                </button>
                <button
                  type="button"
                  @click="formatText('insertOrderedList')"
                  class="px-3 py-1 text-sm hover:bg-gray-100"
                  title="有序列表"
                >
                  1. 列表
                </button>
              </div>

              <!-- 插入 -->
              <div class="flex border border-gray-300 rounded">
                <button
                  type="button"
                  @click="insertLink"
                  class="px-3 py-1 text-sm hover:bg-gray-100 border-r border-gray-300"
                  title="插入链接"
                >
                  🔗 链接
                </button>
                <button
                  type="button"
                  @click="insertImage"
                  class="px-3 py-1 text-sm hover:bg-gray-100"
                  title="插入图片"
                >
                  🖼️ 图片
                </button>
              </div>

              <!-- 清除格式 -->
              <button
                type="button"
                @click="formatText('removeFormat')"
                class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100"
                title="清除格式"
              >
                清除格式
              </button>
            </div>
          </div>

          <!-- 编辑器 -->
          <div
            ref="editor"
            contenteditable="true"
            class="w-full min-h-[400px] px-4 py-3 border border-gray-300 border-t-0 rounded-b-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            @input="updateContent"
            @paste="handlePaste"
          ></div>
          
          <!-- 编辑器提示 -->
          <div class="mt-2 flex justify-between text-sm text-gray-500">
            <span>支持富文本编辑，可以插入图片和链接</span>
            <span>{{ getContentLength() }} 字符</span>
          </div>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useNewsStore } from '@/stores/news'
import { useAuthStore } from '@/stores/auth'

// Composables
const router = useRouter()
const route = useRoute()
const newsStore = useNewsStore()
const authStore = useAuthStore()

// 响应式数据
const loading = ref(false)
const pageLoading = ref(true)
const newTag = ref('')
const editor = ref<HTMLElement>()
const newsItem = ref<any>(null)

// 分类数据
const categories = ref([
  { id: '1', name: '公司新闻' },
  { id: '2', name: '产品发布' },
  { id: '3', name: '技术分享' },
  { id: '4', name: '行业动态' }
])

// 表单数据
const form = reactive({
  title: '',
  content: '',
  excerpt: '',
  category_id: '',
  status: 'draft',
  tags: [] as string[]
})

// 计算属性
const isFormValid = computed(() => {
  return form.title.trim() &&
         form.content.trim() &&
         form.excerpt.trim() &&
         form.category_id &&
         form.status
})

// 方法
const goBack = () => {
  if (hasUnsavedChanges()) {
    if (confirm('您有未保存的更改，确定要离开吗？')) {
      router.push('/admin/news')
    }
  } else {
    router.push('/admin/news')
  }
}

const hasUnsavedChanges = () => {
  if (!newsItem.value) return false

  return form.title !== newsItem.value.title ||
         form.content !== newsItem.value.content ||
         form.excerpt !== newsItem.value.excerpt ||
         form.category_id !== newsItem.value.category_id ||
         form.status !== newsItem.value.status ||
         JSON.stringify(form.tags) !== JSON.stringify(newsItem.value.tags)
}

const loadNews = async () => {
  const newsId = route.params.id as string
  if (!newsId) {
    router.push('/admin/news')
    return
  }

  try {
    pageLoading.value = true

    // 确保新闻数据已加载
    if (newsStore.news.length === 0) {
      await newsStore.fetchNews()
    }

    // 查找新闻
    const news = newsStore.getNewsById(newsId)
    if (!news) {
      console.error('新闻不存在:', newsId)
      return
    }

    // 检查权限
    if (!authStore.isAdmin && news.author_id !== authStore.user?.id) {
      alert('您没有权限编辑此新闻')
      router.push('/admin/news')
      return
    }

    newsItem.value = news

    // 初始化表单
    Object.assign(form, {
      title: news.title,
      content: news.content,
      excerpt: news.excerpt,
      category_id: news.category_id,
      status: news.status,
      tags: [...news.tags]
    })

    // 设置编辑器内容
    await nextTick()
    if (editor.value) {
      editor.value.innerHTML = news.content
    }

  } catch (error) {
    console.error('加载新闻失败:', error)
    alert('加载新闻失败')
  } finally {
    pageLoading.value = false
  }
}

const updateContent = () => {
  if (editor.value) {
    form.content = editor.value.innerHTML
  }
}

const getContentLength = () => {
  if (editor.value) {
    return editor.value.textContent?.length || 0
  }
  return 0
}

const formatText = (command: string) => {
  document.execCommand(command, false)
  editor.value?.focus()
}

const insertLink = () => {
  const url = prompt('请输入链接地址:')
  if (url) {
    document.execCommand('createLink', false, url)
    editor.value?.focus()
  }
}

const insertImage = () => {
  const url = prompt('请输入图片地址:')
  if (url) {
    document.execCommand('insertImage', false, url)
    editor.value?.focus()
  }
}

const handlePaste = (event: ClipboardEvent) => {
  event.preventDefault()
  const text = event.clipboardData?.getData('text/plain') || ''
  document.execCommand('insertText', false, text)
}

const addTag = () => {
  const tag = newTag.value.trim()
  if (tag && !form.tags.includes(tag) && form.tags.length < 10) {
    form.tags.push(tag)
    newTag.value = ''
  }
}

const removeTag = (index: number) => {
  form.tags.splice(index, 1)
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

const saveDraft = async () => {
  if (!form.title.trim()) {
    alert('请输入标题')
    return
  }

  loading.value = true
  try {
    const data = {
      ...form,
      status: 'draft'
    }

    await newsStore.updateNews(newsItem.value.id, data)
    console.log('✅ 草稿保存成功')

    // 更新本地数据
    Object.assign(newsItem.value, data)

    alert('草稿保存成功！')
  } catch (error) {
    console.error('❌ 保存草稿失败:', error)
    alert('保存失败: ' + (error as Error).message)
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  if (!isFormValid.value) {
    alert('请填写所有必填字段')
    return
  }

  loading.value = true
  try {
    await newsStore.updateNews(newsItem.value.id, form)
    console.log('✅ 新闻更新成功')

    // 根据状态显示不同的成功消息
    let message = ''
    if (form.status === 'draft') {
      message = '新闻已保存为草稿！'
    } else if (form.status === 'pending') {
      message = '新闻已提交审批！'
    } else if (form.status === 'published') {
      message = '新闻已发布！'
    }

    alert(message)
    router.push('/admin/news')
  } catch (error) {
    console.error('❌ 更新新闻失败:', error)
    alert('更新失败: ' + (error as Error).message)
  } finally {
    loading.value = false
  }
}

// 生命周期
onMounted(async () => {
  console.log('✅ 新闻编辑页面已加载')

  // 检查用户是否已登录
  if (!authStore.isAuthenticated) {
    router.push('/')
    return
  }

  await loadNews()
})

// 页面离开前的确认
window.addEventListener('beforeunload', (event) => {
  if (hasUnsavedChanges()) {
    event.preventDefault()
    event.returnValue = ''
  }
})
</script>

<style scoped>
/* 编辑器样式 */
[contenteditable="true"]:focus {
  outline: none;
}

[contenteditable="true"] p {
  margin: 0.5em 0;
}

[contenteditable="true"] ul,
[contenteditable="true"] ol {
  margin: 0.5em 0;
  padding-left: 2em;
}

[contenteditable="true"] img {
  max-width: 100%;
  height: auto;
  margin: 0.5em 0;
}

[contenteditable="true"] a {
  color: #3b82f6;
  text-decoration: underline;
}
</style>
