<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="flex items-center justify-between">
      <h2 class="text-2xl font-bold text-gray-900">
        {{ isEdit ? '编辑新闻' : '创建新闻' }}
      </h2>
      <router-link
        to="/admin/news"
        class="text-gray-600 hover:text-gray-900 text-sm font-medium"
      >
        返回列表
      </router-link>
    </div>

    <!-- 表单 -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <form @submit.prevent="handleSubmit" class="space-y-6">
        <!-- 标题 -->
        <div>
          <label for="title" class="block text-sm font-medium text-gray-700 mb-1">
            标题 <span class="text-red-500">*</span>
          </label>
          <input
            id="title"
            v-model="form.title"
            type="text"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入新闻标题"
          />
        </div>

        <!-- 摘要 -->
        <div>
          <label for="excerpt" class="block text-sm font-medium text-gray-700 mb-1">
            摘要 <span class="text-red-500">*</span>
          </label>
          <textarea
            id="excerpt"
            v-model="form.excerpt"
            rows="3"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入新闻摘要"
          ></textarea>
        </div>

        <!-- 分类 -->
        <div>
          <label for="category" class="block text-sm font-medium text-gray-700 mb-1">
            分类 <span class="text-red-500">*</span>
          </label>
          <select
            id="category"
            v-model="form.category_id"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">请选择分类</option>
            <option v-for="category in categories" :key="category.id" :value="category.id">
              {{ category.name }}
            </option>
          </select>
        </div>

        <!-- 标签 -->
        <div>
          <label for="tags" class="block text-sm font-medium text-gray-700 mb-1">
            标签
          </label>
          <input
            id="tags"
            v-model="tagsInput"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入标签，用逗号分隔"
          />
          <p class="text-xs text-gray-500 mt-1">多个标签请用逗号分隔，例如：技术,分享,Vue</p>
        </div>

        <!-- 内容 -->
        <div>
          <label for="content" class="block text-sm font-medium text-gray-700 mb-1">
            内容 <span class="text-red-500">*</span>
          </label>
          <textarea
            id="content"
            v-model="form.content"
            rows="12"
            required
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="请输入新闻内容，支持HTML格式"
          ></textarea>
          <p class="text-xs text-gray-500 mt-1">支持HTML格式，例如：&lt;h2&gt;标题&lt;/h2&gt; &lt;p&gt;段落&lt;/p&gt;</p>
        </div>

        <!-- 状态 -->
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-1">
            发布状态
          </label>
          <div class="space-y-2">
            <label class="flex items-center">
              <input
                v-model="form.status"
                type="radio"
                value="draft"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <span class="ml-2 text-sm text-gray-700">保存为草稿</span>
            </label>
            <label class="flex items-center">
              <input
                v-model="form.status"
                type="radio"
                :value="authStore.isAdmin ? 'published' : 'pending'"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
              />
              <span class="ml-2 text-sm text-gray-700">
                {{ authStore.isAdmin ? '立即发布' : '提交审批' }}
              </span>
            </label>
          </div>
          <p v-if="!authStore.isAdmin" class="text-xs text-gray-500 mt-1">
            编辑用户需要管理员审批后才能发布
          </p>
        </div>

        <!-- 错误信息 -->
        <div v-if="error" class="text-red-600 text-sm">
          {{ error }}
        </div>

        <!-- 按钮组 -->
        <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
          <router-link
            to="/admin/news"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
          >
            取消
          </router-link>
          <button
            type="submit"
            :disabled="loading"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span v-if="loading" class="flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isEdit ? '更新中...' : '创建中...' }}
            </span>
            <span v-else>
              {{ isEdit ? '更新新闻' : '创建新闻' }}
            </span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useNewsStore } from '@/stores/news'
import { categoryApi } from '@/lib/vue-api'
import type { Category, CreateNewsData } from '@/types/vue-types'

// Props
interface Props {
  id?: string
}

const props = defineProps<Props>()

// Composables
const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const newsStore = useNewsStore()

// Reactive data
const categories = ref<Category[]>([])
const loading = ref(false)
const error = ref<string | null>(null)
const tagsInput = ref('')

const form = reactive<CreateNewsData>({
  title: '',
  content: '',
  excerpt: '',
  category_id: '',
  status: 'draft',
  tags: [],
})

// Computed
const isEdit = computed(() => !!props.id || !!route.params.id)

// Methods
const loadCategories = async () => {
  try {
    const data = await categoryApi.getCategories()
    categories.value = data
  } catch (err) {
    console.error('加载分类失败:', err)
  }
}

const loadNews = async () => {
  if (!isEdit.value) return
  
  try {
    const newsId = props.id || route.params.id as string
    const news = await newsStore.fetchNewsById(newsId)
    
    Object.assign(form, {
      title: news.title,
      content: news.content,
      excerpt: news.excerpt,
      category_id: news.category_id,
      status: news.status,
      tags: news.tags || [],
    })
    
    tagsInput.value = (news.tags || []).join(', ')
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载新闻失败'
  }
}

const handleSubmit = async () => {
  try {
    loading.value = true
    error.value = null
    
    // 处理标签
    form.tags = tagsInput.value
      .split(',')
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0)
    
    if (isEdit.value) {
      const newsId = props.id || route.params.id as string
      await newsStore.updateNews({ id: newsId, ...form })
    } else {
      await newsStore.createNews(form)
    }
    
    router.push('/admin/news')
  } catch (err) {
    error.value = err instanceof Error ? err.message : '操作失败'
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(async () => {
  await Promise.all([
    loadCategories(),
    loadNews()
  ])
})
</script>
