<template>
  <div class="space-y-6">
    <!-- 页面标题和操作 -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">数据统计</h1>
        <p class="text-gray-600 mt-1">新闻数据分析和报表</p>
      </div>
      
      <div class="flex space-x-3">
        <select
          v-model="selectedTimeRange"
          class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="7">最近7天</option>
          <option value="30">最近30天</option>
          <option value="90">最近90天</option>
          <option value="365">最近一年</option>
        </select>
        <button
          @click="refreshData"
          :disabled="loading"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
        >
          <svg class="h-4 w-4 mr-2" :class="{ 'animate-spin': loading }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          刷新数据
        </button>
        <button
          @click="exportReport"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          <svg class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          导出报表
        </button>
      </div>
    </div>

    <!-- 核心指标卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <!-- 总新闻数 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总新闻数</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.totalNews }}</p>
            <p class="text-sm text-green-600">+{{ stats.newsGrowth }}% 较上期</p>
          </div>
        </div>
      </div>

      <!-- 总浏览量 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
            <svg class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">总浏览量</p>
            <p class="text-2xl font-bold text-gray-900">{{ formatNumber(stats.totalViews) }}</p>
            <p class="text-sm text-green-600">+{{ stats.viewsGrowth }}% 较上期</p>
          </div>
        </div>
      </div>

      <!-- 活跃用户 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
            <svg class="h-5 w-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">活跃用户</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.activeUsers }}</p>
            <p class="text-sm text-blue-600">{{ stats.userGrowth }}% 活跃度</p>
          </div>
        </div>
      </div>

      <!-- 平均阅读时长 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center">
          <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
            <svg class="h-5 w-5 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">平均阅读时长</p>
            <p class="text-2xl font-bold text-gray-900">{{ stats.avgReadTime }}</p>
            <p class="text-sm text-green-600">+{{ stats.readTimeGrowth }}% 较上期</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 新闻发布趋势 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-semibold text-gray-900">新闻发布趋势</h3>
          <select
            v-model="chartTimeRange"
            class="text-sm border border-gray-300 rounded px-2 py-1"
          >
            <option value="7">7天</option>
            <option value="30">30天</option>
            <option value="90">90天</option>
          </select>
        </div>
        <div class="h-64 flex items-center justify-center bg-gray-50 rounded">
          <div class="text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <p class="mt-2 text-sm text-gray-500">新闻发布趋势图表</p>
            <p class="text-xs text-gray-400">（图表组件待集成）</p>
          </div>
        </div>
      </div>

      <!-- 分类分布 -->
      <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">分类分布</h3>
        <div class="space-y-3">
          <div
            v-for="category in categoryStats"
            :key="category.id"
            class="flex items-center justify-between"
          >
            <div class="flex items-center space-x-3">
              <div 
                class="w-3 h-3 rounded-full"
                :style="{ backgroundColor: category.color }"
              ></div>
              <span class="text-sm font-medium text-gray-900">{{ category.name }}</span>
            </div>
            <div class="flex items-center space-x-2">
              <div class="w-24 bg-gray-200 rounded-full h-2">
                <div 
                  class="h-2 rounded-full"
                  :style="{ 
                    width: `${(category.count / categoryStats.reduce((sum, c) => sum + c.count, 0)) * 100}%`,
                    backgroundColor: category.color 
                  }"
                ></div>
              </div>
              <span class="text-sm text-gray-600 w-8 text-right">{{ category.count }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详细数据表格 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 热门新闻 -->
      <div class="bg-white rounded-lg shadow-sm">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">热门新闻 TOP 10</h3>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div
              v-for="(news, index) in topNews"
              :key="news.id"
              class="flex items-center space-x-4"
            >
              <div 
                class="w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold"
                :class="index < 3 ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-600'"
              >
                {{ index + 1 }}
              </div>
              <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-gray-900 truncate">{{ news.title }}</p>
                <p class="text-xs text-gray-500">{{ news.category }} • {{ formatDate(news.created_at) }}</p>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-gray-900">{{ formatNumber(news.views) }}</p>
                <p class="text-xs text-gray-500">浏览量</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 用户活动 -->
      <div class="bg-white rounded-lg shadow-sm">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-semibold text-gray-900">用户活动统计</h3>
        </div>
        <div class="p-6">
          <div class="space-y-4">
            <div
              v-for="user in userActivity"
              :key="user.id"
              class="flex items-center justify-between"
            >
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                  {{ user.name.charAt(0).toUpperCase() }}
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ user.name }}</p>
                  <p class="text-xs text-gray-500">{{ user.role }}</p>
                </div>
              </div>
              <div class="text-right">
                <p class="text-sm font-medium text-gray-900">{{ user.newsCount }}</p>
                <p class="text-xs text-gray-500">篇新闻</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时数据 -->
    <div class="bg-white rounded-lg shadow-sm">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h3 class="text-lg font-semibold text-gray-900">实时数据监控</h3>
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span class="text-sm text-gray-500">实时更新</span>
          </div>
        </div>
      </div>
      <div class="p-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div class="text-center">
            <p class="text-2xl font-bold text-blue-600">{{ realTimeStats.onlineUsers }}</p>
            <p class="text-sm text-gray-500">在线用户</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold text-green-600">{{ realTimeStats.todayViews }}</p>
            <p class="text-sm text-gray-500">今日浏览</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold text-purple-600">{{ realTimeStats.todayNews }}</p>
            <p class="text-sm text-gray-500">今日发布</p>
          </div>
          <div class="text-center">
            <p class="text-2xl font-bold text-yellow-600">{{ realTimeStats.pendingApproval }}</p>
            <p class="text-sm text-gray-500">待审批</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { useNewsStore } from '@/stores/news'

// Composables
const authStore = useAuthStore()
const newsStore = useNewsStore()

// 响应式数据
const loading = ref(false)
const selectedTimeRange = ref('30')
const chartTimeRange = ref('30')
let realTimeInterval: NodeJS.Timeout | null = null

// 统计数据
const stats = reactive({
  totalNews: 28,
  newsGrowth: 12.5,
  totalViews: 15420,
  viewsGrowth: 8.3,
  activeUsers: 5,
  userGrowth: 85.2,
  avgReadTime: '3.2分钟',
  readTimeGrowth: 5.7
})

// 分类统计
const categoryStats = ref([
  { id: '1', name: '公司新闻', count: 8, color: '#3b82f6' },
  { id: '2', name: '产品发布', count: 5, color: '#10b981' },
  { id: '3', name: '技术分享', count: 12, color: '#f59e0b' },
  { id: '4', name: '行业动态', count: 3, color: '#8b5cf6' }
])

// 热门新闻
const topNews = ref([
  {
    id: '1',
    title: '荣联科技发布新一代AI解决方案',
    category: '产品发布',
    views: 2580,
    created_at: '2024-12-14T10:00:00Z'
  },
  {
    id: '2',
    title: 'Vue 3.0最佳实践分享',
    category: '技术分享',
    views: 1920,
    created_at: '2024-12-13T14:30:00Z'
  },
  {
    id: '3',
    title: '公司年度总结大会成功举办',
    category: '公司新闻',
    views: 1650,
    created_at: '2024-12-12T16:00:00Z'
  },
  {
    id: '4',
    title: '行业数字化转型趋势分析',
    category: '行业动态',
    views: 1420,
    created_at: '2024-12-11T09:15:00Z'
  },
  {
    id: '5',
    title: '新员工培训计划启动',
    category: '公司新闻',
    views: 1280,
    created_at: '2024-12-10T11:45:00Z'
  },
  {
    id: '6',
    title: 'TypeScript进阶技巧',
    category: '技术分享',
    views: 1150,
    created_at: '2024-12-09T15:20:00Z'
  },
  {
    id: '7',
    title: '客户成功案例分享',
    category: '产品发布',
    views: 980,
    created_at: '2024-12-08T13:10:00Z'
  },
  {
    id: '8',
    title: '团队建设活动回顾',
    category: '公司新闻',
    views: 850,
    created_at: '2024-12-07T17:30:00Z'
  },
  {
    id: '9',
    title: '前端性能优化实战',
    category: '技术分享',
    views: 720,
    created_at: '2024-12-06T10:45:00Z'
  },
  {
    id: '10',
    title: '市场趋势报告发布',
    category: '行业动态',
    views: 650,
    created_at: '2024-12-05T14:00:00Z'
  }
])

// 用户活动统计
const userActivity = ref([
  { id: 'user1', name: '张三', role: '编辑', newsCount: 12 },
  { id: 'user2', name: '李四', role: '编辑', newsCount: 8 },
  { id: 'user3', name: '王五', role: '编辑', newsCount: 5 },
  { id: 'admin1', name: '系统管理员', role: '管理员', newsCount: 3 }
])

// 实时数据
const realTimeStats = reactive({
  onlineUsers: 3,
  todayViews: 1250,
  todayNews: 2,
  pendingApproval: 2
})

// 方法
const formatNumber = (num: number) => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24))

  if (diffDays === 0) {
    return '今天'
  } else if (diffDays === 1) {
    return '昨天'
  } else if (diffDays < 7) {
    return `${diffDays}天前`
  } else {
    return date.toLocaleDateString('zh-CN')
  }
}

const refreshData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 更新统计数据
    stats.totalNews = Math.floor(Math.random() * 10) + 25
    stats.totalViews = Math.floor(Math.random() * 5000) + 12000
    stats.activeUsers = Math.floor(Math.random() * 3) + 4

    console.log('✅ 数据刷新成功')
  } catch (error) {
    console.error('❌ 刷新数据失败:', error)
  } finally {
    loading.value = false
  }
}

const exportReport = () => {
  // 模拟导出功能
  const reportData = {
    timeRange: selectedTimeRange.value,
    stats: stats,
    categoryStats: categoryStats.value,
    topNews: topNews.value.slice(0, 5),
    userActivity: userActivity.value,
    exportTime: new Date().toISOString()
  }

  const dataStr = JSON.stringify(reportData, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)

  const link = document.createElement('a')
  link.href = url
  link.download = `news-report-${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  URL.revokeObjectURL(url)

  console.log('✅ 报表导出成功')
}

const updateRealTimeData = () => {
  // 模拟实时数据更新
  realTimeStats.onlineUsers = Math.floor(Math.random() * 3) + 2
  realTimeStats.todayViews += Math.floor(Math.random() * 10)

  // 随机更新今日新闻数
  if (Math.random() < 0.1) {
    realTimeStats.todayNews += 1
  }

  // 随机更新待审批数
  if (Math.random() < 0.05) {
    realTimeStats.pendingApproval = Math.max(0, realTimeStats.pendingApproval + (Math.random() < 0.5 ? 1 : -1))
  }
}

// 计算属性
const totalCategoryNews = computed(() => {
  return categoryStats.value.reduce((sum, category) => sum + category.count, 0)
})

// 生命周期
onMounted(async () => {
  console.log('✅ 数据统计页面已加载')

  // 检查管理员权限
  if (!authStore.isAdmin) {
    alert('您没有权限访问此页面')
    return
  }

  // 初始化数据
  await refreshData()

  // 启动实时数据更新
  realTimeInterval = setInterval(updateRealTimeData, 5000)
})

onUnmounted(() => {
  // 清理定时器
  if (realTimeInterval) {
    clearInterval(realTimeInterval)
  }
})
</script>

<style scoped>
/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* 进度条样式 */
.progress-bar {
  transition: width 0.3s ease-in-out;
}

/* 卡片悬停效果 */
.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
}
</style>
