<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <router-link to="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
              <NewspaperIcon class="h-5 w-5 text-white" />
            </div>
            <h1 class="text-xl font-bold text-gray-900">荣联科技新闻中心</h1>
          </router-link>
          <router-link
            to="/"
            class="text-gray-600 hover:text-gray-900 text-sm font-medium"
          >
            返回首页
          </router-link>
        </div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div v-if="loading" class="flex justify-center items-center h-64">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>

      <div v-else-if="error" class="text-center py-12">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">新闻不存在</h2>
        <p class="text-gray-600 mb-6">{{ error }}</p>
        <router-link
          to="/"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
        >
          返回首页
        </router-link>
      </div>

      <article v-else-if="news" class="bg-white rounded-lg shadow-sm overflow-hidden">
        <div class="px-6 py-8">
          <!-- 标题 -->
          <h1 class="text-3xl font-bold text-gray-900 mb-6">
            {{ news.title }}
          </h1>

          <!-- 元信息 -->
          <div class="flex flex-wrap items-center gap-6 mb-6 text-sm text-gray-600 border-b border-gray-200 pb-6">
            <div class="flex items-center">
              <FolderIcon class="h-4 w-4 mr-2" />
              <span>{{ news.category.name }}</span>
            </div>
            <div class="flex items-center">
              <UserIcon class="h-4 w-4 mr-2" />
              <span>{{ news.author.name }}</span>
            </div>
            <div class="flex items-center">
              <CalendarIcon class="h-4 w-4 mr-2" />
              <span>{{ formatDate(news.published_at || news.created_at) }}</span>
            </div>
            <div class="flex items-center">
              <EyeIcon class="h-4 w-4 mr-2" />
              <span>{{ news.view_count }} 次浏览</span>
            </div>
          </div>

          <!-- 标签 -->
          <div v-if="news.tags && news.tags.length > 0" class="mb-6">
            <div class="flex flex-wrap gap-2">
              <span
                v-for="(tag, index) in news.tags"
                :key="index"
                class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800"
              >
                {{ tag }}
              </span>
            </div>
          </div>

          <!-- 摘要 -->
          <div v-if="news.excerpt" class="mb-8 p-4 bg-gray-50 rounded-lg border-l-4 border-blue-500">
            <p class="text-gray-700 italic">{{ news.excerpt }}</p>
          </div>

          <!-- 正文内容 -->
          <div class="prose max-w-none" v-html="news.content"></div>
        </div>

        <!-- 底部信息 -->
        <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
          <div class="flex justify-between items-center">
            <div class="text-sm text-gray-500">
              最后更新: {{ formatDateTime(news.updated_at) }}
            </div>
            <router-link
              to="/"
              class="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              返回新闻列表
            </router-link>
          </div>
        </div>
      </article>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import {
  NewspaperIcon,
  FolderIcon,
  UserIcon,
  CalendarIcon,
  EyeIcon,
} from '@heroicons/vue/24/outline'
import { useNewsStore } from '@/stores/news'
import type { NewsWithDetails } from '@/types/vue-types'

// Props
interface Props {
  id: string
}

const props = defineProps<Props>()

// Composables
const route = useRoute()
const newsStore = useNewsStore()

// Reactive data
const news = ref<NewsWithDetails | null>(null)
const loading = ref(true)
const error = ref<string | null>(null)

// Methods
const formatDate = (dateString: string) => {
  return format(new Date(dateString), 'yyyy年MM月dd日', { locale: zhCN })
}

const formatDateTime = (dateString: string) => {
  return format(new Date(dateString), 'yyyy年MM月dd日 HH:mm', { locale: zhCN })
}

const loadNews = async () => {
  try {
    loading.value = true
    error.value = null
    
    const newsId = props.id || route.params.id as string
    const newsData = await newsStore.fetchNewsById(newsId)
    
    // 只显示已发布的新闻
    if (newsData.status !== 'published') {
      throw new Error('新闻未发布或不存在')
    }
    
    news.value = newsData
    
    // 增加浏览量
    await newsStore.incrementViewCount(newsId)
  } catch (err) {
    error.value = err instanceof Error ? err.message : '加载新闻失败'
  } finally {
    loading.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadNews()
})
</script>
