<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue 3 应用调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>🔧 Vue 3 应用调试页面</h1>
    
    <div class="debug-section">
        <h2>📊 应用状态检查</h2>
        <div id="app-status" class="status info">检查中...</div>
        <button onclick="checkAppStatus()">重新检查</button>
    </div>

    <div class="debug-section">
        <h2>🌐 网络连接测试</h2>
        <div id="network-status" class="status info">测试中...</div>
        <button onclick="testNetwork()">测试连接</button>
    </div>

    <div class="debug-section">
        <h2>📝 控制台日志</h2>
        <div id="console-logs"></div>
        <button onclick="clearLogs()">清空日志</button>
    </div>

    <div class="debug-section">
        <h2>🔗 快速链接</h2>
        <button onclick="openApp()">打开应用</button>
        <button onclick="openDevTools()">打开开发者工具</button>
    </div>

    <script>
        // 捕获控制台日志
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        const logs = [];

        function addLog(type, message) {
            const timestamp = new Date().toLocaleTimeString();
            logs.push({ type, message, timestamp });
            updateLogsDisplay();
        }

        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog('log', args.join(' '));
        };

        console.error = function(...args) {
            originalError.apply(console, args);
            addLog('error', args.join(' '));
        };

        console.warn = function(...args) {
            originalWarn.apply(console, args);
            addLog('warn', args.join(' '));
        };

        function updateLogsDisplay() {
            const logsDiv = document.getElementById('console-logs');
            logsDiv.innerHTML = logs.slice(-10).map(log => 
                `<div class="status ${log.type === 'error' ? 'error' : log.type === 'warn' ? 'info' : 'success'}">
                    [${log.timestamp}] ${log.type.toUpperCase()}: ${log.message}
                </div>`
            ).join('');
        }

        function checkAppStatus() {
            const statusDiv = document.getElementById('app-status');
            statusDiv.innerHTML = '检查中...';
            statusDiv.className = 'status info';

            fetch('http://localhost:3000/')
                .then(response => {
                    if (response.ok) {
                        statusDiv.innerHTML = '✅ Vue 3 应用运行正常！';
                        statusDiv.className = 'status success';
                        return response.text();
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                })
                .then(html => {
                    if (html.includes('Vue') || html.includes('app')) {
                        console.log('应用页面加载成功');
                    } else {
                        console.warn('页面内容可能不完整');
                    }
                })
                .catch(error => {
                    statusDiv.innerHTML = `❌ 连接失败: ${error.message}`;
                    statusDiv.className = 'status error';
                    console.error('应用状态检查失败:', error);
                });
        }

        function testNetwork() {
            const statusDiv = document.getElementById('network-status');
            statusDiv.innerHTML = '测试中...';
            statusDiv.className = 'status info';

            const tests = [
                { name: 'Vue 3 应用', url: 'http://localhost:3000/' },
                { name: 'Vite 开发服务器', url: 'http://localhost:3000/@vite/client' }
            ];

            Promise.all(tests.map(test => 
                fetch(test.url)
                    .then(response => ({ ...test, status: response.status, ok: response.ok }))
                    .catch(error => ({ ...test, status: 'ERROR', error: error.message }))
            )).then(results => {
                const allOk = results.every(r => r.ok);
                statusDiv.innerHTML = `
                    ${allOk ? '✅' : '❌'} 网络测试${allOk ? '通过' : '失败'}
                    <pre>${JSON.stringify(results, null, 2)}</pre>
                `;
                statusDiv.className = `status ${allOk ? 'success' : 'error'}`;
            });
        }

        function clearLogs() {
            logs.length = 0;
            updateLogsDisplay();
        }

        function openApp() {
            window.open('http://localhost:3000/', '_blank');
        }

        function openDevTools() {
            alert('请按 F12 或右键选择"检查元素"打开开发者工具');
        }

        // 页面加载时自动检查
        window.onload = function() {
            checkAppStatus();
            testNetwork();
            console.log('Vue 3 调试页面已加载');
        };
    </script>
</body>
</html>
