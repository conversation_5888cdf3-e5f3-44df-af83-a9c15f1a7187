# 公司新闻发布系统

一个基于 Next.js 和 Supabase 构建的现代化新闻发布与管理系统。

## 功能特性

- ✨ **现代化界面**: 使用 Tailwind CSS 构建的响应式设计
- 📝 **富文本编辑**: 基于 TipTap 的强大富文本编辑器
- 🗂️ **分类管理**: 支持新闻分类和标签系统
- 👥 **用户权限**: 支持管理员和编辑角色
- 🔍 **搜索筛选**: 支持按标题、内容、分类、状态筛选
- 📊 **数据统计**: 浏览量统计和发布状态管理
- 🔐 **安全认证**: 基于 Supabase 的用户认证系统

## 技术栈

- **前端框架**: Next.js 14 (App Router)
- **样式**: Tailwind CSS
- **富文本编辑器**: TipTap
- **后端服务**: Supabase (数据库 + 认证 + 存储)
- **UI 组件**: Headless UI + Heroicons
- **表单处理**: React Hook Form + Zod
- **语言**: TypeScript

## 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd newssystem
```

### 2. 安装依赖

```bash
npm install
```

### 3. 设置 Supabase

1. 在 [Supabase](https://supabase.com) 创建新项目
2. 复制项目 URL 和 anon key
3. 更新 `.env.local` 文件：

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
```

### 4. 初始化数据库

在 Supabase SQL 编辑器中运行 `database/schema.sql` 文件中的 SQL 语句来创建表结构和初始数据。

### 5. 启动开发服务器

```bash
npm run dev
```

打开 [http://localhost:3000](http://localhost:3000) 查看应用。

## 项目结构

```
src/
├── app/                    # Next.js App Router 页面
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 主页
├── components/            # React 组件
│   ├── NewsList.tsx       # 新闻列表组件
│   ├── NewsForm.tsx       # 新闻表单组件
│   └── RichTextEditor.tsx # 富文本编辑器组件
├── lib/                   # 工具库
│   ├── api.ts            # API 接口
│   └── supabase.ts       # Supabase 配置
└── types/                 # TypeScript 类型定义
    └── index.ts
```

## 数据库表结构

### users (用户表)
- `id`: 用户ID (UUID)
- `email`: 邮箱
- `name`: 姓名
- `role`: 角色 (admin/editor)
- `avatar_url`: 头像URL
- `created_at`, `updated_at`: 时间戳

### categories (分类表)
- `id`: 分类ID (UUID)
- `name`: 分类名称
- `description`: 描述
- `slug`: URL 友好的标识符
- `created_at`, `updated_at`: 时间戳

### news (新闻表)
- `id`: 新闻ID (UUID)
- `title`: 标题
- `content`: 内容 (HTML)
- `excerpt`: 摘要
- `category_id`: 分类ID (外键)
- `author_id`: 作者ID (外键)
- `status`: 状态 (draft/published/archived)
- `featured_image`: 特色图片URL
- `published_at`: 发布时间
- `view_count`: 浏览量
- `tags`: 标签数组
- `created_at`, `updated_at`: 时间戳

## 使用说明

### 创建新闻
1. 点击"创建新闻"按钮
2. 填写标题、摘要、选择分类
3. 使用富文本编辑器编写内容
4. 添加标签（可选）
5. 选择状态（草稿/发布）
6. 点击"创建"保存

### 管理新闻
- **编辑**: 点击新闻列表中的编辑图标
- **删除**: 点击删除图标并确认
- **筛选**: 使用搜索框和下拉菜单筛选新闻
- **状态管理**: 通过编辑功能更改新闻状态

### 权限说明
- **管理员**: 可以管理所有新闻和分类
- **编辑**: 只能管理自己创建的新闻

## 部署

### Vercel 部署

1. 将代码推送到 GitHub
2. 在 [Vercel](https://vercel.com) 导入项目
3. 设置环境变量
4. 部署

### 其他平台

确保设置正确的环境变量：
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`

## 开发指南

### 添加新功能

1. 在 `src/types/index.ts` 中定义类型
2. 在 `src/lib/api.ts` 中添加 API 函数
3. 创建相应的 React 组件
4. 更新数据库 schema（如需要）

### 自定义样式

项目使用 Tailwind CSS，可以在 `tailwind.config.js` 中自定义主题。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License
