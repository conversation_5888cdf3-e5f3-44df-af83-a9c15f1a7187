{"name": "newssystem-vue", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "@supabase/supabase-js": "^2.38.4", "@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "date-fns": "^2.30.0", "@tiptap/vue-3": "^2.1.13", "@tiptap/starter-kit": "^2.1.13", "@tiptap/extension-placeholder": "^2.1.13", "@tiptap/extension-link": "^2.1.13", "@tiptap/extension-image": "^2.1.13", "zod": "^3.22.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.5.2", "vite": "^5.0.8", "vue-tsc": "^1.8.25", "typescript": "^5.2.2", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@types/node": "^20.10.5"}}