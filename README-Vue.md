# 荣联科技新闻中心 - Vue 3 版本

基于 Vue 3 + TypeScript + Vite + Tailwind CSS + Pinia 构建的新闻发布管理系统。

## 🚀 技术栈

- **前端框架**: Vue 3 (Composition API)
- **开发语言**: TypeScript
- **构建工具**: Vite
- **样式框架**: Tailwind CSS
- **状态管理**: Pinia
- **路由管理**: Vue Router 4
- **UI组件**: Headless UI
- **图标库**: Heroicons
- **日期处理**: date-fns
- **后端服务**: Supabase (可选，支持模拟数据)

## 📁 项目结构

```
src/
├── components/          # 可复用组件
│   ├── SimpleNewsList.vue    # 简化的新闻列表
│   ├── LoginModal.vue        # 登录模态框
│   └── NewsViewer.vue        # 新闻查看器
├── views/              # 页面组件
│   ├── Home.vue             # 首页
│   ├── Admin.vue            # 管理页面布局
│   └── admin/               # 管理子页面
│       └── NewsManagement.vue
├── stores/             # Pinia 状态管理
│   ├── auth.ts             # 认证状态
│   └── news.ts             # 新闻状态
├── lib/                # 工具库
│   ├── vue-api.ts          # API 接口
│   ├── vue-supabase.ts     # Supabase 配置
│   └── vue-mockData.ts     # 模拟数据
├── types/              # TypeScript 类型定义
│   └── vue-types.ts
├── router/             # 路由配置
│   └── index.ts
└── style.css           # 全局样式
```

## 🛠️ 安装和运行

### 1. 安装依赖

```bash
# 使用 Vue 3 版本的 package.json
cp package-vue.json package.json

# 安装依赖
npm install
```

### 2. 环境配置

```bash
# 复制环境变量文件
cp .env.example .env

# 编辑 .env 文件，配置 Supabase（可选）
# 如果不配置 Supabase，系统将使用模拟数据
```

### 3. 启动开发服务器

```bash
npm run dev
```

应用将在 `http://localhost:3000` 启动。

### 4. 构建生产版本

```bash
npm run build
```

## 🎯 功能特性

### 公开访问（未登录用户）
- ✅ 查看已发布的新闻列表（13条模拟新闻）
- ✅ 搜索新闻（按标题和内容）
- ✅ 按分类筛选新闻
- ✅ 点击新闻查看详细内容
- ✅ 响应式设计，支持移动端
- ✅ 登录功能（模态框）

### 管理功能（已登录用户）
- ✅ 新闻管理（创建、编辑、删除、查看）
- ✅ 新闻审批（仅管理员）
- ✅ 用户管理（仅管理员）
- ✅ 角色权限控制（管理员/编辑）
- ✅ 状态管理和路由守卫

## 👥 测试账号

系统提供以下测试账号：

| 角色 | 邮箱 | 密码 | 权限 |
|------|------|------|------|
| 管理员 | <EMAIL> | admin123 | 全部功能 |
| 编辑 | <EMAIL> | editor123 | 新闻管理 |
| 编辑 | <EMAIL> | editor123 | 新闻管理 |

## 📊 数据说明

### 模拟新闻数据
系统包含 **13条已发布的新闻**，涵盖以下分类：
- 公司新闻：公司活动、获奖、合作等
- 行业动态：行业趋势、政策法规等  
- 产品发布：新产品、功能更新等
- 技术分享：技术文章、经验分享等

### 新闻状态
- `published`: 已发布（首页显示）
- `pending`: 待审批（需管理员审批）
- `draft`: 草稿（编辑中）
- `rejected`: 已拒绝（审批被拒）

## 🔧 开发说明

### 状态管理
使用 Pinia 进行状态管理：
- `useAuthStore`: 用户认证状态
- `useNewsStore`: 新闻数据状态

### 路由配置
- `/`: 首页（公开新闻列表）
- `/admin`: 管理页面（需要登录）
- `/admin/news`: 新闻管理
- `/admin/approval`: 新闻审批（仅管理员）
- `/admin/users`: 用户管理（仅管理员）

### API 设计
支持 Supabase 和模拟数据两种模式：
- 如果配置了 Supabase，使用真实数据库
- 如果未配置，自动使用模拟数据

## 🎨 样式系统

使用 Tailwind CSS 构建响应式界面：
- 移动优先的响应式设计
- 一致的颜色和间距系统
- 平滑的过渡动画
- 可访问性支持

## 🚀 部署

### Vercel 部署
```bash
npm run build
# 将 dist 目录部署到 Vercel
```

### Netlify 部署
```bash
npm run build
# 将 dist 目录部署到 Netlify
```

## 📝 更新日志

### v1.0.0 (Vue 3 版本)
- ✅ 完整的 Vue 3 + TypeScript 重构
- ✅ 使用 Composition API 和 Pinia
- ✅ 响应式设计和现代化 UI
- ✅ 完整的权限管理系统
- ✅ 13条模拟新闻数据
- ✅ 支持 Supabase 和模拟数据

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
